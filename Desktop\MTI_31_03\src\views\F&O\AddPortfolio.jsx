import React, { useState, useEffect, useRef } from "react";
import { useParams } from "react-router-dom";
import Modal from "react-modal";
import "./AddPortfolio.css";
import Timepic from "../../components/Timepic";
import { useNavigate, useLocation } from "react-router-dom";
import Fandorow from "../F&O/FandOrow";
import Cookies from "universal-cookie";
import { useSelector, useDispatch } from "react-redux";
import { setConsoleMsgs } from "../../store/slices/consoleMsg";
import { RotatingLines } from "react-loader-spinner";
import { useFetchPortfolios } from "../../hooks/useFetchPortfolios";
import { AnimatePresence, motion } from "framer-motion";
import StrategySelector from "./StrategySelector";
import { fetchWithAuth } from "../../utils/api";


const cookies = new Cookies();

function AnimatedCheckIcon({ initial = true, isVisible }) {
  return (
    <AnimatePresence initial={initial}>
      {isVisible && (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={3}
          stroke="#4661BD"
          className="CheckIcon"
        >
          <motion.path
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            exit={{ pathLength: 0 }}
            transition={{
              type: "tween",
              duration: 1,
              ease: isVisible ? "easeOut" : "easeIn",
            }}
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M4.5 12.75l6 6 9-13.5"
          />
        </svg>
      )}
    </AnimatePresence>
  );
}

function AddPortfolio() {
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  const fetchPortfolios = useFetchPortfolios();

  const handleMsg = (Msg) => {
    dispatch((dispatch, getState) => {
      const previousConsoleMsgs = getState().consoleMsgsReducer.consoleMsgs;

      const lastMsg = previousConsoleMsgs[ 0 ];
      if (
        lastMsg &&
        lastMsg.msg === Msg.msg &&
        lastMsg.user === Msg.user &&
        lastMsg.strategy === Msg.startegy &&
        lastMsg.portfolio === Msg.porttfolio
      ) {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ Msg, ...previousConsoleMsgs.slice(1) ],
          })
        );
      } else {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ Msg, ...previousConsoleMsgs ],
          })
        );
      }
    });
  };
  const weekdays = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];
  const [ selectedWeekdays, setSelectedWeekdays ] = useState([]);
  const [ selectAllChecked, setSelectAllChecked ] = useState(false);
  const [ dropdownVisible, setDropdownVisible ] = useState(false);
  const [ isDropdownOpen, setIsDropdownOpen ] = useState(false);

  const [ predefinedStrategy, setPredefinedStrategy ] = useState("");

  const dropdownRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownVisible(false);
        setIsDropdownOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [ dropdownRef ]);
  useEffect(() => {
    const allWeekdaysSelected = selectedWeekdays.length === weekdays.length;
    setSelectAllChecked(allWeekdaysSelected);
  }, [ selectedWeekdays, weekdays ]);

  const toggleWeekday = (weekday) => {
    const updatedSelectedWeekdays = selectedWeekdays.includes(weekday)
      ? selectedWeekdays.filter((day) => day !== weekday)
      : [ ...selectedWeekdays, weekday ];
    const sortedSelectedWeekdays = updatedSelectedWeekdays.sort((a, b) => {
      return weekdays.indexOf(a) - weekdays.indexOf(b);
    });
    setSelectedWeekdays(sortedSelectedWeekdays);
    setIsDropdownOpen(false);
  };

  const toggleDropdown = () => {
    setDropdownVisible(!dropdownVisible);
    setIsDropdownOpen(!isDropdownOpen);
  };

  const toggleSelectAll = () => {
    if (!selectAllChecked) {
      setSelectedWeekdays([ ...weekdays ]);
    } else {
      setSelectedWeekdays([]);
    }
    setSelectAllChecked();
  };

  const handleOk = () => {
    setDropdownVisible(false);
    setIsDropdownOpen(false);
  };

  const handleCancel = () => {
    setSelectedWeekdays([]);
    setDropdownVisible(false);
    setIsDropdownOpen(false);
  };

  const params = useParams();
  const FandRowRef = useRef(null);

  const [ editPortfolio, seteditPortfolio ] = useState(false);

  const [ margin, setmargin ] = useState(() => {
    if (params.portfolio) {
      return JSON.parse(params.portfolio).margin;
    } else {
      return "0";
    }
  });

  const [ isPortfolioExecuted, setisPortfolioExecuted ] = useState(false);

  useEffect(() => {
    const fetchExecutedPortfolios = async () => {
      try {
        const response = await fetchWithAuth(
          `/api/get_executed_portfolios/${mainUser}`,
          {
            method: "POST",
          }
        );
        if (!response.ok) {
          throw new Error("Failed to fetch executed portfolios");
        }
        const { ExecutedPortfolios } = await response.json();

        if (
          ExecutedPortfolios.some(
            (execPort) => execPort.portfolio_name === portfolio.portfolio_name
          )
        ) {
          setisPortfolioExecuted(true);
        }
      } catch (error) { }
    };
    if (editPortfolio) {
      fetchExecutedPortfolios();
    }
  }, []);

  const [ portfolio, setPortfolio ] = useState(() => {
    if (params.portfolio) {
      seteditPortfolio(true);
      console.log(" JSON.parse(params.portfolio)", JSON.parse(params.portfolio));
      return JSON.parse(params.portfolio);
    } else {
      seteditPortfolio(false);
      return null;
    }
  });

  useEffect(() => {
    if (pathname.includes("Edit-Portfolio")) {
      if (params.portfolio) {
        seteditPortfolio(true);
        setPortfolio(JSON.parse(params.portfolio));
      } else {
        seteditPortfolio(false);
        setPortfolio(null);
      }
    }
  }, [ pathname ]);

  const [ showModal, setShowModal ] = useState(false);

  const handleSymbolChange = (e) => {
    setIsPortfolioEdited(true);
    const symbol = e.target.value;
    setstock_symbol(symbol);
  };

  const [ isExecutionTabActive, setIsExecutionTabActive ] = useState(true);
  const [ isTargetTabActive, setisTargetTabActive ] = useState(false);
  const [ isStoplossTabActive, setisStoplossTabActive ] = useState(false);
  const [ isExitTabActive, setisExitTabActive ] = useState(false);

  const [ selectedType, setSelectedType ] = useState("");
  const [ isCheckedcheck, setIsCheckedcheck ] = useState(false);
  const navigate = useNavigate();

  const [ activeTab, setActiveTab ] = useState("execution");

  const handleTabClick = (tab) => {
    setActiveTab(tab);
    setIsExecutionTabActive(tab === "execution");
    setisTargetTabActive(tab === "target");
    setisStoplossTabActive(tab === "stoploss");
    setisExitTabActive(tab === "exit");
    setisExtraConditions(tab === "extra");
    setisOtherTabActive(tab === "other");
  };
  const [ legsEdited, setlegsEdited ] = useState(false);

  const [ strategyTags, setStrategyTags ] = useState([]);

  useEffect(() => {
    const fetchStrategy = async (username) => {
      try {
        const response = await fetchWithAuth(
          `/api/retrieve_strategy_info/${username}`,
          {
            method: "GET",
          }
        );
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const responseData = await response.json();
        const extractedStrategyTags = responseData.strategies.map(
          (strategy) => strategy.strategy_tag
        );
        setStrategyTags(extractedStrategyTags);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchStrategy(cookies.get("USERNAME"));
  }, []);

  const [ exchange, setExchange ] = useState("NFO");
  const [ stock_symbol, setstock_symbol ] = useState("NIFTY");
  const [ selectedDate, setSelectedDate ] = React.useState(null);
  const [ product_type, setProduct ] = useState("");
  const [ max_lots, setMaxLots ] = useState("");
  const [ selectedStrategy, setSelectedStrategy ] = useState("");
  const [ order_type, setEntryOrder ] = useState("");
  const [ portfolio_name, setPortfolioName ] = useState("");
  const [ limit_price, setPrice ] = useState();

  const [ message, setMessage ] = useState("");

  function getModalWidth(message) {
    const baseWidth = 150;
    const extraWidth = Math.min(300, Math.ceil(message.length / 50) * 100);
    return `${baseWidth + extraWidth}px`;
  }
  function getModalHeight(message) {
    const baseHeight = 100;
    const extraHeight = Math.min(200, Math.ceil(message.length / 50) * 50);
    return `${baseHeight + extraHeight}px`;
  }

  const mainUser = cookies.get("USERNAME");
  const [ IsPortfolioEdited, setIsPortfolioEdited ] = useState(false);

  const [ isExtraConditions, setisExtraConditions ] = useState(false);
  const [ isOtherTabActive, setisOtherTabActive ] = useState(false);

  const [ isExtraConditionsChecked, setisExtraConditionsChecked ] =
    useState(false);
  const [ selectedOptionminuteclose, setSelectedOptionminuteclose ] =
    useState("");

  const [ dateOptions, setdateOptions ] = useState([]);
  const expiryState = useSelector((state) => state.expiryReducer);

  useEffect(() => {
    const generateDateOptions = () => {
      if (!Object.values(expiryState).includes([])) {
        const currentLegs = FandRowRef.current.getLegs();
        const hasFutures = currentLegs.some((leg) => leg.option_type === "FUT");

        let Expirylist = [];
        if (stock_symbol === "NIFTY") {
          Expirylist = hasFutures
            ? [ ...new Set([ ...expiryState.NIFTY, ...expiryState.FUTNIFTY ]) ]
            : expiryState.NIFTY;
        } else if (stock_symbol === "BANKNIFTY") {
          Expirylist = hasFutures
            ? [
              ...new Set([
                ...expiryState.BANKNIFTY,
                ...expiryState.FUTBANKNIFTY,
              ]),
            ]
            : expiryState.BANKNIFTY;
        } else if (stock_symbol === "FINNIFTY") {
          Expirylist = hasFutures
            ? [
              ...new Set([ ...expiryState.FINNIFTY, ...expiryState.FUTFINNIFTY ]),
            ]
            : expiryState.FINNIFTY;
        }

        if (!editPortfolio) {
          setSelectedDate(Expirylist[ 0 ]);
        }

        const options = Expirylist.map((expiry) => (
          <option
            selected={
              editPortfolio
                ? portfolio.expiry_date === expiry
                : selectedDate === expiry
                  ? true
                  : false
            }
            key={`${stock_symbol}-${expiry}`}
            value={expiry}
          >
            {expiry}
          </option>
        ));

        setdateOptions(options);
      }
    };

    generateDateOptions();
  }, [ stock_symbol, expiryState ]);

  const [ clickedSave, setclickedSave ] = useState(false);

  const handleGoBackClick = () => {
    if (editPortfolio) {
      let edited = false;
      const valuesMatch =
        exchange === portfolio.exchange &&
        stock_symbol === portfolio.stock_symbol &&
        selectedStrategy === portfolio.strategy &&
        order_type === portfolio.order_type &&
        portfolio.product_type === product_type &&
        sqOffTime === portfolio.square_off_time &&
        startTime === portfolio.start_time &&
        isChecked === portfolio.buy_trades_first &&
        endTime === portfolio.end_time &&
        isCheckedPortfolio === portfolio.positional_portfolio;

      const currentLegs = FandRowRef.current.getLegs();
      const dbLegs = JSON.parse(params.portfolio).legs;

      if (currentLegs.length !== dbLegs.length) {
        edited = true;
      }

      currentLegs.sort((a, b) => a.id - b.id);
      dbLegs.sort((a, b) => a.id - b.id);

      for (let i = 0; i < currentLegs.length; i++) {
        const obj1 = currentLegs[ i ];
        const obj2 = dbLegs[ i ];

        for (let key in obj2) {
          if (key !== "trail_sl" && key !== "trail_tgt") {
            if (obj1[ key ] !== obj2[ key ]) {
              edited = true;
            }
          } else {
            if (
              !(
                obj1[ key ].length === obj2[ key ].length &&
                obj1[ key ].every((value, index) => value === obj2[ key ][ index ])
              )
            ) {
              edited = true;
            }
          }
        }
      }

      if (
        ((!edited || clickedSave) && (valuesMatch || clickedSave)) ||
        isPortfolioExecuted
      ) {
        navigate("/F&O/Portfolio");
      } else {
        setIsPortfolioEdited(edited);
        setShowModal(true);
        setMessage("Please save the changes");
      }
    } else {
      let edited = false;
      const valuesMatch =
        exchange === "NFO" &&
        stock_symbol === "NIFTY" &&
        selectedStrategy === "" &&
        order_type === "" &&
        product_type === "";

      const currentLegs = FandRowRef.current.getLegs();
      if (currentLegs.length !== 1) {
        edited = true;
      }

      const leg = currentLegs[ 0 ];
      edited =
        edited ||
        (leg.transaction_type === "BUY" &&
          leg.option_type === "CE" &&
          leg.lots === 1 &&
          leg.expiry_date === selectedDate &&
          leg.strike === "ATM");

      if (valuesMatch || clickedSave) {
        navigate("/F&O/Portfolio");
      } else {
        setIsPortfolioEdited(edited);
        setShowModal(true);
        setMessage("Please save the changes");
      }
    }
  };

  const [ startTime, setStartTime ] = useState("00:00:00");
  const [ endTime, setEndTime ] = useState("00:00:00");
  const [ sqOffTime, setSqOffTime ] = useState("");

  const handleTimeChange = (label, time) => {
    let isTimeChanged = false;

    if (label === "startTime") {
      if (time !== startTime) {
        setStartTime(time);
        isTimeChanged = true;
      }
    } else if (label === "endTime") {
      if (time !== endTime) {
        setEndTime(time);
        isTimeChanged = true;
      }
    } else if (label === "sqOffTime") {
      if (time !== sqOffTime) {
        setSqOffTime(time);
        isTimeChanged = true;
      }
    }

    if (isTimeChanged) {
      setIsPortfolioEdited(true);
    }
  };
  const [ isCheckedPortfolio, setIsCheckedPortfolio ] = useState(false);

  const handleCheckboxChangePortfolio = (event) => {
    setIsCheckedPortfolio(event.target.checked);
    setIsPortfolioEdited(true);
  };
  const [ isChecked, setIsChecked ] = useState(false);

  const handleCheckboxChange = (event) => {
    setIsChecked(event.target.checked);
    setIsPortfolioEdited(true);
  };

  useEffect(() => {
    if (editPortfolio) {
      setExchange(portfolio.exchange);
      setstock_symbol(portfolio.stock_symbol);
      setPortfolioName(portfolio.portfolio_name);
      setProduct(portfolio.product_type);
      setPrice(portfolio.limit_price);
      setEntryOrder(portfolio.order_type);
      setSelectedStrategy(portfolio.strategy);
      setSqOffTime(portfolio.square_off_time);
      setEndTime(portfolio.end_time);
      setStartTime(portfolio.start_time);
      setIsChecked(portfolio.buy_trades_first);
      setIsCheckedPortfolio(portfolio.positional_portfolio);
      setMaxLots(portfolio.max_lots);
    }
  }, []);

  const [ gotAllLTP, setgotAllLTP ] = useState(false);

  const { portfolios } = useSelector((state) => state.portfolioReducer);

  const [ savingPortfolio, setsavingPortfolio ] = useState(false);
  const [ isCheckIconVisible, setIsCheckIconVisible ] = useState(false);

  useEffect(() => {
    if (showModal) {
      setsavingPortfolio(false);
    }
  }, [ showModal ]);

  const [ loading, setLoading ] = useState(false);

  const [ valueAllLots, setValueAllLots ] = useState();
  const [ valuePerLot, setValuePerLot ] = useState();
  const { brokers: rows } = useSelector((state) => state.brokerReducer);

  const handleRefreshLtp = async () => {
    const hasValidAccount = rows.some(
      (account) => account.broker !== "pseudo_account" && account.inputDisabled
    );

    if (!hasValidAccount) {
      setMessage(
        "Please ensure at least one non-pseudo account is logged in to proceed."
      );
      setShowModal(true);
      return;
    }
    setLoading(true);

    const currentLegs = FandRowRef.current.getLegs();
    for (let index = 0; index < currentLegs.length; index++) {
      const leg = currentLegs[ index ];

      if (!leg.expiry_date) {
        setMessage("Please Select Expiry Date for the portfolio.");
        setShowModal(true);
        return;
      }
    }
    await FandRowRef.current.getLegLTP();

    const allLTPs = currentLegs?.map((leg) => leg.ltp);
    const allLots = currentLegs?.map((leg) => leg.lots);

    if (allLTPs) {
      const extractedValues = allLTPs?.map((value) => {
        if (value && typeof value === "string") {
          const match = value.match(/\(([^)]+)\)/);
          return match ? match[ 1 ] : null;
        }
        return null;
      });
      let totalValue = 0;
      for (let i = 0; i < currentLegs.length; i++) {
        if (extractedValues[ i ]) {
          totalValue += extractedValues[ i ] * allLots[ i ];
        }
      }
      setValueAllLots(totalValue);
      setValuePerLot(totalValue);
      setLoading(false);
    }
  };

  const handleSavePortfolio = async () => {
    try {
      const currentLegs = FandRowRef.current.getLegs();
      const showError = (message) => {
        setShowModal(true);
        setMessage(message);
        return false;
      };

      if (!editPortfolio) {
        const existingNames = portfolios.map((p) =>
          p.portfolio_name.toLowerCase()
        );
        if (existingNames.includes(portfolio_name.toLowerCase())) {
          return showError("Please enter a unique name for the Portfolio");
        }
        if (!portfolio_name)
          return showError("Please enter a name for the Portfolio");
        if (/\s/.test(portfolio_name))
          return showError("Portfolio name cannot contain spaces");
      }

      const requiredFields = [
        [ exchange, "Exchange" ],
        [ product_type, "Product" ],
        [ selectedStrategy, "Strategy Tag" ],
        [ order_type, "Entry Order Type" ],
      ];
      for (const [ field, name ] of requiredFields) {
        if (!field) return showError(`Please select a ${name}`);
      }

      const isValidTime = (time, isPortfolioChecked) => {
        if (time === "00:00:00") return true;

        const timeObj = new Date(`1970-01-01T${time}`);
        const hours = timeObj.getHours();
        const minutes = timeObj.getMinutes();

        const isValid =
          (hours > 9 || (hours === 9 && minutes >= 15)) &&
          (hours < 15 || (hours === 15 && minutes <= 30));

        return isPortfolioChecked ? true : isValid;
      };

      const validateTime = () => {
        if (!isValidTime(startTime, false))
          return showError("Start time should be between 9:15 AM and 3:30 PM");

        if (!isValidTime(endTime, false))
          return showError("End time should be between 9:15 AM and 3:30 PM");

        if (!isValidTime(sqOffTime, isCheckedPortfolio))
          return showError(
            "Square off time should be between 9:15 AM and 3:30 PM"
          );
      };
      validateTime();

      if (isCheckedPortfolio) {
        let largestLegStartTime = null;
        const today = new Date();

        const expiryDateList = [];
        const startTimelist = [];

        const monthMap = {
          JAN: 0,
          FEB: 1,
          MAR: 2,
          APR: 3,
          MAY: 4,
          JUN: 5,
          JUL: 6,
          AUG: 7,
          SEP: 8,
          OCT: 9,
          NOV: 10,
          DEC: 11,
        };
        for (let index = 0; index < currentLegs.length; index++) {
          const leg = currentLegs[ index ];

          if (!leg.start_time) {
            setShowModal(true);
            setMessage(`For Leg ${index + 1}, start_time is not present`);
            return;
          }

          if (leg.expiry_date && sqOffTime !== "00:00:00" && sqOffTime !== "") {
            const expiryDateStr = leg.expiry_date;

            const dayStr = expiryDateStr.slice(0, 2);
            const monthStr = expiryDateStr.slice(2, 5);
            const yearStr = expiryDateStr.slice(5, 9);

            const monthIndex = monthMap[ monthStr.toUpperCase() ];
            const legExpiryDateObj = new Date(yearStr, monthIndex, dayStr);
            expiryDateList.push(legExpiryDateObj);

            if (leg.start_time) {
              const startTimeStr = leg.start_time;

              const [ dayStr, monthStr, timeStr ] = startTimeStr.split(" ");

              const day = parseInt(dayStr, 10);
              const monthIndex = monthMap[ monthStr.toUpperCase() ];
              const [ hours, minutes, seconds ] = timeStr.split(":").map(Number);
              const today = new Date();
              const legStartDate = new Date(
                today.getFullYear(),
                monthIndex,
                day,
                hours,
                minutes,
                seconds
              );

              startTimelist.push(legStartDate);
            }
            const legStartDateObj = new Date(
              `${yearStr}-${monthIndex + 1}-${dayStr}`
            );
            if (!largestLegStartTime || legStartDateObj > largestLegStartTime) {
              largestLegStartTime = legStartDateObj;
            }

            if (largestLegStartTime) {
              const legStartDateFormatted = largestLegStartTime
                .toISOString()
                .split("T")[ 0 ];

              const [ dayStr, monthStr, timeStr ] = sqOffTime.split(" ");
              const day = parseInt(dayStr, 10);
              const monthIndexFromSqOff = monthMap[ monthStr.toUpperCase() ];

              const sqOffDate = new Date(
                today.getFullYear(),
                monthIndexFromSqOff,
                day,
                ...timeStr.split(":")
              );

              const marketOpenTime = new Date(
                today.getFullYear(),
                monthIndexFromSqOff,
                day,
                9,
                15
              );
              const marketCloseTime = new Date(
                today.getFullYear(),
                monthIndexFromSqOff,
                day,
                15,
                30
              );

              if (sqOffDate < marketOpenTime || sqOffDate > marketCloseTime) {
                setShowModal(true);
                setMessage(
                  "Square-off time should be between 9:15 AM and 3:30 PM"
                );
                return;
              }

              const dateTimeRegex =
                /^(\d{2})\s([A-Za-z]{3})\s(\d{2}):(\d{2}):(\d{2})$/;
              const match = sqOffTime.match(dateTimeRegex);
              if (!match && !isNaN(sqOffDate)) {
                setShowModal(true);
                setMessage(
                  'Please enter the Square-off time in "DD MMM HH:MM:SS" format, e.g., "14 Oct 13:03:49"'
                );
                return;
              }
            }
          }
        }

        if (startTimelist.length > 0) {
          const largestStartTime = startTimelist.reduce((latest, current) => {
            return current > latest ? current : latest;
          });

          const [ dayStr, monthStr, timeStr ] = sqOffTime.split(" ");
          const day = parseInt(dayStr, 10);
          const monthIndexFromSqOff = monthMap[ monthStr.toUpperCase() ];
          const [ hours, minutes, seconds ] = timeStr.split(":").map(Number);
          const SqOffDate = new Date(
            today.getFullYear(),
            monthIndexFromSqOff,
            day,
            hours,
            minutes,
            seconds
          );
          if (largestStartTime > SqOffDate) {
            setShowModal(true);
            setMessage(
              "Square-off date should be on or before the Square off date and time."
            );
            return;
          }
        }
        if (expiryDateList.length > 0) {
          const latestExpiryDate = expiryDateList.reduce((a, b) => (a > b ? a : b));

          const [ dayStr, monthStr, timeStr ] = sqOffTime.split(" ");
          const day = parseInt(dayStr, 10);
          const monthIndexFromSqOff = monthMap[ monthStr.toUpperCase() ];

          const latestExpiryMonth = latestExpiryDate.getMonth();
          const latestExpiryDay = latestExpiryDate.getDate();

          const sqOffMonth = monthIndexFromSqOff;
          const sqOffDay = day;

          if (
            sqOffMonth > latestExpiryMonth ||
            (sqOffMonth === latestExpiryMonth && sqOffDay > latestExpiryDay)
          ) {
            setShowModal(true);
            setMessage(
              "Square-off date should be on or before the expiry date."
            );
            return;
          }
        }
      }
      if (isCheckedPortfolio) {
        const today = new Date();
        const currentDate = today.toISOString().split("T")[ 0 ];
        const firstLegStartTime = currentLegs[ 0 ].start_time;
        const [ dayStr, monthStr, time ] = firstLegStartTime.split(" ");

        const day = parseInt(dayStr, 10);
        const monthIndex = new Date(Date.parse(monthStr + " 1, 2024")).getMonth();
        const legStartDateObj = new Date(today.getFullYear(), monthIndex, day);
        const year = legStartDateObj.getFullYear();
        const month = String(legStartDateObj.getMonth() + 1).padStart(2, "0");
        const days = String(legStartDateObj.getDate()).padStart(2, "0");

        const formattedDate = `${year}-${month}-${days}`;
        const dateTimeRegex =
          /^(\d{2})\s([A-Za-z]{3})\s(\d{2}):(\d{2}):(\d{2})$/;
        const match = firstLegStartTime.match(dateTimeRegex);

        if (!match) {
          setShowModal(true);
          setMessage(
            'Invalid format. Please enter the date in "DD MMM HH:MM:SS" format, e.g., "14 Oct 13:03:49"'
          );
          return;
        }
        if (formattedDate !== currentDate) {
          setShowModal(true);
          setMessage("The first leg's start_time must be today");
          return;
        }

        const legStartTime = new Date(`${currentDate}T${time}`);

        const marketOpenTime = new Date(`${currentDate}T09:30:00`);
        const marketCloseTime = new Date(`${currentDate}T15:30:00`);

        if (legStartTime < marketOpenTime || legStartTime > marketCloseTime) {
          setShowModal(true);
          setMessage(
            "The first leg's start_time must be today between 9:30 AM and 3:30 PM."
          );
          return;
        }

        for (let index = 1; index < currentLegs.length; index++) {
          const leg = currentLegs[ index ];
          const legStartTime = leg.start_time;

          const dateTimeRegex =
            /^(\d{2})\s([A-Za-z]{3})\s(\d{2}):(\d{2}):(\d{2})$/;
          const match = legStartTime.match(dateTimeRegex);
          if (!match) {
            setShowModal(true);
            setMessage(
              `For Leg ${index + 1
              }, Please enter the date in "DD MMM HH:MM:SS" format, e.g., "14 Oct 13:03:49"`
            );
            return;
          }

          if (!legStartTime) {
            setShowModal(true);
            setMessage(`For Leg ${index + 1}, start_time is not present`);
            return;
          }
          const expirationDate = currentLegs[ index ].expiry_date;
          const dayStrexpirationDate = expirationDate.slice(0, 2);
          const monthStrexpirationDate = expirationDate.slice(2, 5);
          const yearStrexpirationDate = expirationDate.slice(5);

          const dayexpirationDate = parseInt(dayStrexpirationDate, 10);
          const yearexpirationDate = parseInt(yearStrexpirationDate, 10);
          const monthIndexexpirationDate = new Date(
            Date.parse(monthStrexpirationDate + " 1, 2024")
          ).getMonth();

          const formattedExpirationDate = `${yearexpirationDate}-${String(
            monthIndexexpirationDate + 1
          ).padStart(2, "0")}-${String(dayexpirationDate).padStart(2, "0")}`;
          const [ dayStr, monthStr, time ] = legStartTime.split(" ");
          const day = parseInt(dayStr, 10);
          const monthIndex = new Date(
            Date.parse(monthStr + " 1, 2024")
          ).getMonth();

          const today = new Date();
          const legStartDateObj = new Date(
            today.getFullYear(),
            monthIndex,
            day
          );

          const year = legStartDateObj.getFullYear();
          const month = String(legStartDateObj.getMonth() + 1).padStart(2, "0");
          const days = String(legStartDateObj.getDate()).padStart(2, "0");
          const formattedLegStartDate = `${year}-${month}-${days}`;

          if (
            formattedLegStartDate < currentDate ||
            formattedLegStartDate > formattedExpirationDate
          ) {
            setShowModal(true);
            setMessage(
              `For Leg ${index + 1
              }, the start_time must be between today and the expiration date.`
            );
            return;
          }

          const legStartDateTime = new Date(`${formattedLegStartDate}T${time}`);
          const marketOpenTime = new Date(`${formattedLegStartDate}T09:15:00`);
          const marketCloseTime = new Date(`${formattedLegStartDate}T15:30:00`);

          if (
            legStartDateTime < marketOpenTime ||
            legStartDateTime > marketCloseTime
          ) {
            setShowModal(true);
            setMessage(
              `For Leg ${index + 1
              }, the start_time must be between 9:15 AM and 3:30 PM.`
            );
            return;
          }
        }
      }
      if (!isCheckedPortfolio) {
        for (let index = 0; index < currentLegs.length; index++) {
          const leg = currentLegs[ index ];

          const today = new Date();
          const currentDate = today.toISOString().split("T")[ 0 ];
          const firstLegStartTime = leg.start_time.trim();

          const timeRegex = /^(\d{2}):(\d{2}):(\d{2})$/;
          if (!timeRegex.test(firstLegStartTime) && firstLegStartTime) {
            setShowModal(true);
            setMessage(
              `For Leg ${index + 1}, start_time must be in "00:00:00" format.`
            );
            return;
          }

          const legStartTime = new Date(`${currentDate}T${firstLegStartTime}`);
          const marketOpenTime = new Date(`${currentDate}T09:30:00`);
          const marketCloseTime = new Date(`${currentDate}T15:30:00`);

          if (legStartTime < marketOpenTime || legStartTime > marketCloseTime) {
            setShowModal(true);
            setMessage(
              `For Leg ${index + 1
              }, start_time must be between 9:30 AM and 3:30 PM.`
            );
            return;
          }
        }
      }

      for (let index = 0; index < currentLegs.length; index++) {
        const leg = currentLegs[ index ];
        if (
          leg.expiry_date === "" ||
          leg.expiry_date === undefined ||
          leg.expiry_date === null
        ) {
          setShowModal(true);
          setMessage(`For Leg ${index + 1}, Please select an Expiry Date`);
          return;
        }
      }
      for (let index = 0; index < currentLegs.length; index++) {
        if (order_type !== "LIMIT" && order_type !== "SL_LIMIT") {
          const leg = currentLegs[ index ];
          leg.limit_price = "";
        }
      }

      const allStrikes = currentLegs.map((leg) => leg.strike);
      if (
        allStrikes.includes("") ||
        allStrikes.includes(null) ||
        allStrikes.includes(undefined)
      ) {
        setShowModal(true);
        setMessage("Please select a Strike Price");
        return;
      }

      await FandRowRef.current.getLegLTP();

      const tgt_value = currentLegs.map((leg) => leg.tgt_value);
      const sl_value = currentLegs.map((leg) => leg.sl_value);
      let allLTPs = currentLegs.map((leg) => leg.ltp);
      const target = currentLegs.map((leg) => leg.target);
      const stop_loss = currentLegs.map((leg) => leg.stop_loss);
      let limitPrices = currentLegs.map((leg) => parseFloat(leg.limit_price));
      let ltpValues = extractLtpPrices(allLTPs);
      const option_type = currentLegs.map((leg) => leg.option_type);

      let tgtErrorLegs = [];
      let slErrorLegs = [];

      for (let i = 0; i < currentLegs.length; i++) {
        console.log(
          currentLegs[ i ].limit_price,
          ltpValues[ i ],
          order_type,
          "sb",
          currentLegs[ i ].wait_sec
        );

        if (
          target[ i ] === "Absolute Premium" &&
          tgt_value[ i ] <= ltpValues[ i ] &&
          ltpValues[ i ] !== 0 &&
          tgt_value[ i ] !== 0
        ) {
          tgtErrorLegs.push(i + 1);
        }
        if (
          stop_loss[ i ] === "Absolute Premium" &&
          sl_value[ i ] >= ltpValues[ i ] &&
          sl_value[ i ] !== 0 &&
          sl_value[ i ] !== 0
        ) {
          slErrorLegs.push(i + 1);
        }

        if (order_type === "LIMIT") {
          const errorMessages = [];

          for (let i = 0; i < currentLegs.length; i++) {
            console.log(`Validating Leg ${i + 1}:`, currentLegs[ i ]);

            if (
              currentLegs[ i ].transaction_type === "SELL" &&
              currentLegs[ i ].option_type === "FUT" &&
              currentLegs[ i ].limit_price < allLTPs[ i ]
            ) {
              errorMessages.push(
                `For Leg ${i + 1}, Limit Price (${currentLegs[ i ].limit_price
                }) must be greater than or equal to LTP (${allLTPs[ i ]}).`
              );
            }

            if (
              currentLegs[ i ].transaction_type === "BUY" &&
              currentLegs[ i ].option_type === "FUT" &&
              currentLegs[ i ].limit_price > allLTPs[ i ]
            ) {
              errorMessages.push(
                `For Leg ${i + 1}, Limit Price (${currentLegs[ i ].limit_price
                }) must be less than or equal to LTP (${allLTPs[ i ]}).`
              );
            }

            if (
              currentLegs[ i ].transaction_type === "SELL" &&
              [ "CE", "PE" ].includes(currentLegs[ i ].option_type) &&
              currentLegs[ i ].limit_price <= ltpValues[ i ]
            ) {
              errorMessages.push(
                `For Leg ${i + 1}, Limit Price (${currentLegs[ i ].limit_price
                }) must be greater than LTP (${ltpValues[ i ]}).`
              );
            }

            if (
              currentLegs[ i ].transaction_type === "BUY" &&
              [ "CE", "PE" ].includes(currentLegs[ i ].option_type) &&
              currentLegs[ i ].limit_price >= ltpValues[ i ]
            ) {
              errorMessages.push(
                `For Leg ${i + 1}, Limit Price (${currentLegs[ i ].limit_price
                }) must be less than LTP (${ltpValues[ i ]}).`
              );
            }
          }

          if (errorMessages.length > 0) {
            setShowModal(true);
            setMessage(errorMessages.join("\n"));
            return;
          } else {
            console.log(
              "All legs passed validation.",
              ltpValues[ i ],
              currentLegs[ i ].limit_price
            );
          }
        }

        if (
          order_type === "SL_LIMIT" &&
          (!currentLegs[ i ].wait_sec ||
            currentLegs[ i ].wait_sec <= 0 ||
            !currentLegs[ i ].wait_action ||
            currentLegs[ i ].wait_action === "None")
        ) {
          setShowModal(true);
          setMessage(
            `For Leg ${i + 1
            }, wait seconds and a valid On wait action must be present.`
          );
          return;
        }

        if (order_type === "SL_LIMIT") {
          const errorMessages = [];

          for (let i = 0; i < currentLegs.length; i++) {
            console.log(`Validating Leg ${i + 1}:`, currentLegs[ i ]);

            if (
              isNaN(limitPrices[ i ]) ||
              limitPrices[ i ] <= 1 ||
              limitPrices[ i ] >= ltpValues[ i ]
            ) {
              errorMessages.push(
                isNaN(limitPrices[ i ])
                  ? `For Leg ${i + 1
                  }, Limit Price must be a valid number greater than 1.`
                  : `For Leg ${i + 1}, Limit Price (${limitPrices[ i ]
                  }) must be less than LTP (${ltpValues[ i ]}).`
              );
            }
            if (
              currentLegs[ i ].transaction_type === "SELL" &&
              currentLegs[ i ].option_type === "FUT" &&
              limitPrices[ i ] < allLTPs[ i ]
            ) {
              errorMessages.push(
                `For Leg ${i + 1}, Limit Price (${limitPrices[ i ]
                }) must be greater than or equal to LTP (${allLTPs[ i ]}).`
              );
            }

            if (
              currentLegs[ i ].transaction_type === "BUY" &&
              currentLegs[ i ].option_type === "FUT" &&
              limitPrices[ i ] > allLTPs[ i ]
            ) {
              errorMessages.push(
                `For Leg ${i + 1}, Limit Price (${limitPrices[ i ]
                }) must be less than or equal to LTP (${allLTPs[ i ]}).`
              );
            }

            if (
              currentLegs[ i ].transaction_type === "SELL" &&
              [ "CE", "PE" ].includes(currentLegs[ i ].option_type) &&
              limitPrices[ i ] <= ltpValues[ i ]
            ) {
              errorMessages.push(
                `For Leg ${i + 1}, Limit Price (${limitPrices[ i ]
                }) must be greater than LTP (${ltpValues[ i ]}).`
              );
            }

            if (
              currentLegs[ i ].transaction_type === "BUY" &&
              [ "CE", "PE" ].includes(currentLegs[ i ].option_type) &&
              limitPrices[ i ] >= ltpValues[ i ]
            ) {
              errorMessages.push(
                `For Leg ${i + 1}, Limit Price (${limitPrices[ i ]
                }) must be less than LTP (${ltpValues[ i ]}).`
              );
            }
          }

          if (errorMessages.length > 0) {
            setShowModal(true);
            setMessage(errorMessages.join("\n"));
            return;
          } else {
            console.log("All legs passed SL_LIMIT validation.");
          }
        }
      }

      let errorMessages = [];

      if (tgtErrorLegs.length > 0) {
        errorMessages.push(
          `TGT Value should be greater than LTP value for Leg(s): ${tgtErrorLegs.join(
            ", "
          )}`
        );
      }

      if (slErrorLegs.length > 0) {
        errorMessages.push(
          `SL Value should be less than LTP value for Leg(s): ${slErrorLegs.join(
            ", "
          )}`
        );
      }

      if (errorMessages.length > 0) {
        setShowModal(true);
        setMessage(errorMessages.join("\n"));
        return;
      }
      const newPortfolioItem = {
        strategy: selectedStrategy,
        exchange,
        stock_symbol,
        portfolio_name,
        product_type,
        max_lots: max_lots === 0 || max_lots === "" ? 0 : max_lots,
        order_type,
        limit_price,
        legs: currentLegs,
        start_time: startTime,
        end_time: endTime,
        square_off_time: sqOffTime,
        buy_trades_first: isChecked,
        positional_portfolio: isCheckedPortfolio,
        expiry_date: selectedDate,
      };

      const api = {
        endpoint: editPortfolio
          ? `/api/edit_portfolio/${mainUser}/${portfolio_name}`
          : `/api/store_portfolio/${mainUser}`,
      };

      try {
        const response = await fetchWithAuth(api.endpoint, {
          method: "POST",
          body: JSON.stringify(newPortfolioItem),
        });

        const responseData = await response.json();
        if (!response.ok) {
          setShowModal(true);
          setMessage("Something bad happened. Please try again");
        } else {
          setsavingPortfolio(false);
          setIsCheckIconVisible(true);

          setTimeout(() => {
            setIsCheckIconVisible(false);
            navigate("/F&O/Portfolio");
          }, 2000);
          setIsPortfolioEdited(false);
          fetchPortfolios();
          handleMsg({
            msg: responseData[ 0 ].message,
            logType: "MESSAGE",
            timestamp: ` ${new Date().toLocaleString()}`,
            portfolio: newPortfolioItem.portfolio_name,
          });
          if (responseData[ 0 ].message === "Portfolio added successfully") {
            const addToEditParams = JSON.stringify({
              ...newPortfolioItem,
              margin,
            });
            navigate(`/Edit-Portfolio/${addToEditParams}`, {
              ...newPortfolioItem,
              margin,
            });
          }
        }
      } catch (error) {
        setShowModal(true);
        if (error.message === "Portfolio with the same data already exists") {
          setMessage("Portfolio with the same data already exists");
        } else {
          setMessage("Please Try Again");
        }
      }
    } catch (error) {
      setShowModal(true);
      setMessage(error.message || "Something bad happened. Please try again");
    }
  };

  const tooltipStyle = {
    position: "absolute",
    bottom: "100%",
    left: "50%",
    transform: "translateX(-50%)",
    backgroundColor: "black",
    color: "#fff",
    padding: "5px",
    borderRadius: "4px",
    whiteSpace: "nowrap",
    fontSize: "12px",
    zIndex: 100000,
  };
  const [ isFocused, setIsFocused ] = useState(false);

  const extractLtpPrices = (ltpArray) => {
    return ltpArray.map((ltpString) => {
      const match = ltpString?.match(/\(([^)]+)\)/);
      return match ? parseFloat(match[ 1 ]) : null;
    });
  };

  const marketData = useSelector((state) => state.marketReducer);
  const [ timerValue, setTimerValue ] = useState("");

  const getFormattedDateTime = () => {
    const date = new Date();
    const day = String(date.getDate()).padStart(2, "0");
    const months = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = months[ date.getMonth() ];
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${day} ${month} ${hours}:${minutes}:${seconds}`;
  };

  const handleTimerChange = (e) => {
    const newTime = e.target.value;

    setPortfolio((prevPortfolio) => ({
      ...prevPortfolio,
      square_off_time: newTime,
    }));

    setSqOffTime(newTime);
    setIsPortfolioEdited(true);
  };

  useEffect(() => {
    const initialFormattedTime = getFormattedDateTime();
    setTimerValue(initialFormattedTime);
  }, []);

  useEffect(() => {
    if (isCheckedPortfolio) {
      setProduct("NORMAL");
      setPortfolio((prevPortfolio) => ({
        ...prevPortfolio,
        square_off_time: "",
      }));
    }
  }, [ isCheckedPortfolio ]);
  useEffect(() => {
    if (!dateOptions.find((option) => option.props.value === selectedDate)) {
      setSelectedDate(dateOptions[ 0 ]?.props.value || "");
    }
  }, [ dateOptions, selectedDate ]);

  const [ showModelForReset, setShowmodalForReset ] = useState(false);

  const resetTheFields = () => {
    if (!editPortfolio) {
      navigate("/F&O/Portfolio");
      setTimeout(() => {
        navigate("/F&O/AddPortfolio");
      }, 300);
    }
  };
  return (
    <div>
      <div
        className="add-portfolio"
        style={{
          position: "relative",
        }}
      >
        <div className="rectangle-1499"></div>
        <div className="heading">
          <div className="options-portfolio-execution-beta">
            Options Portfolio Execution
          </div>
          <div className="div">
            <div className="div2" onClick={handleGoBackClick}>
              <button
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  fontFamily: "sans-serif",
                  cursor: "pointer",
                }}
              >
                x
              </button>
            </div>
          </div>
        </div>
        <div className="frame-13810">
          <div className="group-13281">
            <div className="default-portfolio-settings">
              Default Portfolio Settings
            </div>
            <div className="help">Help</div>
          </div>
          <div className="marginDetails">
            {stock_symbol === "NIFTY" && (
              <>
                <span
                  className="stockSymbol"
                  style={{
                    fontFamily: "Roboto",
                    fontSize: "14px",
                    fontWeight: "650",
                  }}
                >
                  NIFTY50
                </span>
                <span
                  className="stockLTP"
                  style={
                    marketData?.marketData?.nifty50?.ch < 0
                      ? { color: "red" }
                      : { color: "green" }
                  }
                >
                  {marketData?.marketData?.nifty50?.c}
                </span>
                <span
                  className="lotsizelabel"
                  style={{
                    fontFamily: "Roboto",
                    fontSize: "14px",
                    fontWeight: "650",
                  }}
                >
                  LOTS
                </span>
                <span className="lotsize">75</span>
                <span
                  className="marginLabel"
                  style={{
                    fontFamily: "Roboto",
                    fontSize: "14px",
                    fontWeight: "650",
                  }}
                >
                  Margin Required
                </span>
                <span className="margin">{margin}</span>
              </>
            )}
            {stock_symbol === "BANKNIFTY" && (
              <>
                <span
                  className="stockSymbol"
                  style={{
                    fontFamily: "Roboto",
                    fontSize: "14px",
                    fontWeight: "650",
                  }}
                >
                  BANKNIFTY
                </span>
                <span
                  className="stockLTP"
                  style={
                    marketData?.marketData?.niftybank?.ch < 0
                      ? { color: "red" }
                      : { color: "green" }
                  }
                >
                  {marketData?.marketData?.niftybank?.c}
                </span>
                <span
                  className="lotsizelabel"
                  style={{
                    fontFamily: "Roboto",
                    fontSize: "14px",
                    fontWeight: "650",
                  }}
                >
                  LOTS
                </span>
                <span className="lotsize">30</span>
                <span
                  className="marginLabel"
                  style={{
                    fontFamily: "Roboto",
                    fontSize: "14px",
                    fontWeight: "650",
                  }}
                >
                  Margin Required
                </span>
                <span className="margin">{margin}</span>
              </>
            )}
            {stock_symbol === "FINNIFTY" && (
              <>
                <span
                  className="stockSymbol"
                  style={{
                    fontFamily: "Roboto",
                    fontSize: "14px",
                    fontWeight: "650",
                  }}
                >
                  FINNIFTY
                </span>
                <span
                  className="stockLTP"
                  style={
                    marketData?.marketData?.finnifty?.ch < 0
                      ? { color: "red" }
                      : { color: "green" }
                  }
                >
                  {marketData?.marketData?.finnifty?.c}
                </span>
                <span
                  className="lotsizelabel"
                  style={{
                    fontFamily: "Roboto",
                    fontSize: "14px",
                    fontWeight: "650",
                  }}
                >
                  LOTS
                </span>
                <span className="lotsize">25</span>
                <span
                  className="marginLabel"
                  style={{
                    fontFamily: "Roboto",
                    fontSize: "14px",
                    fontWeight: "650",
                  }}
                >
                  Margin Required
                </span>
                <span className="margin">{margin}</span>
              </>
            )}
          </div>
        </div>
        <div className="frame-13825">
          <div className="frame-13824">
            <div className="exchange">Exchange</div>
            <div className="group-13283">
              <div className="nifty">
                <div className="nifty1" style={{ marginBottom: "3px" }}>
                  Stock Symbol
                </div>
                <select
                  disabled={isPortfolioExecuted}
                  onChange={handleSymbolChange}
                  className="exchange-dropdown1"
                  value={stock_symbol}
                  style={{ cursor: "pointer", width: "85%", height: "36px" }}
                >
                  <option selected disabled>
                    {" "}
                    Select{" "}
                  </option>
                  <option
                    selected={stock_symbol === "NIFTY"}
                    value="NIFTY"
                  >
                    NIFTY
                  </option>
                  <option
                    selected={stock_symbol === "BANKNIFTY"}
                    value="BANKNIFTY"
                  >
                    BANKNIFTY
                  </option>
                  <option
                    selected={stock_symbol === "FINNIFTY"}
                    value="FINNIFTY"
                  >
                    FINNIFTY
                  </option>
                </select>
              </div>
              <div>
                <select
                  disabled={isPortfolioExecuted}
                  className="exchange-dropdown"
                  onChange={(e) => {
                    setExchange(e.target.value);
                    setIsPortfolioEdited(true);
                  }}
                  style={{ cursor: "pointer", width: "45%", height: "36px" }}
                >
                  <option selected disabled>
                    {" "}
                    Select{" "}
                  </option>
                  <option
                    value="BFO"
                    selected={
                      editPortfolio
                        ? portfolio.exchange === "BFO"
                        : exchange === "BFO"
                    }
                  >
                    BFO
                  </option>
                  <option
                    value="NFO"
                    selected={
                      editPortfolio
                        ? portfolio.exchange === "NFO"
                        : exchange === "NFO"
                    }
                  >
                    NFO
                  </option>
                  <option
                    value="MCX"
                    selected={
                      editPortfolio
                        ? portfolio.exchange === "MCX"
                        : exchange === "MCX"
                    }
                  >
                    MCX
                  </option>
                </select>
              </div>
            </div>
          </div>
          <div className="frame-13823">
            <div className="expiry2">Expiry</div>
            <div>
              <select
                disabled={isPortfolioExecuted}
                onChange={(e) => {
                  if (!isPortfolioExecuted) {
                    setIsPortfolioEdited(true);
                    setSelectedDate(e.target.value);
                  }
                }}
                className="expiry-dropdown-main"
                value={
                  dateOptions.find(
                    (option) => option.props.value === selectedDate
                  )
                    ? selectedDate
                    : dateOptions[ 0 ]?.props.value || ""
                }
                style={{ cursor: "pointer", width: "150%", height: "36px" }}
              >
                <option value="" disabled={!editPortfolio}>
                  {editPortfolio ? portfolio.expiry_date : "Select"}
                </option>
                {dateOptions}
              </select>
            </div>
          </div>

          <div className="frame-13822">
            <div className="default-lots">
              Default
              <br />
              Lots
            </div>
            <div className="frame-136682">
              <input
                type="number"
                onInput={(e) => {
                  e.target.value = e.target.value.replace(/[^0-9]/g, "");
                }}
                id="lotsInput"
                defaultValue="1"
                style={{
                  zIndex: "99",
                  paddingTop: "7px",
                  paddingBottom: "7px",
                  marginBottom: "2px",
                }}
              />
            </div>
          </div>

          <div className="frame-13817">
            <div className="predefined-strategies">
              Predefined Strategies
            </div>
            <div className="strategy-selector">
              <StrategySelector
                predefinedStrategy={predefinedStrategy}
                setPredefinedStrategy={setPredefinedStrategy}
                disabled={isPortfolioExecuted}
              />
            </div>
          </div>

          <div className="frame-13818">
            <div className="strike-selection">Strike Selection </div>
            <div>
              <select
                className="strike-selection-dropdown"
                style={{ cursor: "pointer" }}
              >
                <option value="option1">NORMAL</option>
                <option value="option2">RELATIVE</option>
                <option value="option3">BOTH</option>
              </select>
            </div>
          </div>
          <div className="frame-13819">
            <div className="underlying">Underlying</div>
            <div>
              <select
                className="underlying-dropdown"
                style={{ cursor: "pointer" }}
              >
                <option value="option1">SPOT</option>
                <option value="option2">FUTURES</option>
              </select>
            </div>
          </div>
          <div className="frame-13820">
            <div className="price-type">Price Type</div>
            <div>
              <select
                className="price-type-dropdown"
                style={{ cursor: "pointer" }}
              >
                <option value="option1">LTP</option>
                <option value="option2">BID ASK</option>
                <option value="option3">BID ASK AVG</option>
              </select>
            </div>
          </div>
          <div className="frame-13821">
            <div className="strike-step">
              Strike
              <br />
              Step
            </div>
            <div className="group-13282">
              <td style={{ border: "transparent" }}>
                <input type="text" defaultValue={100} />
              </td>
            </div>
          </div>
        </div>
        <div className="frame-13816">
          <div className="frame-13815">
            {/* <div className="frame-137982">
              <input
                type="checkbox"
                id="yourCheckboxId"
                style={{ cursor: "pointer" }}
              />
            </div> */}
            {/* <div className="move-sl-to-cost">Move SL to Cost</div> */}
          </div>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "20px",
              marginTop: "-40px",
            }}
          >
            <div>
              <h2 style={{ fontSize: "14px", marginBottom: "5px" }}>
                Value all Lots
              </h2>
              <input
                type="number"
                value={valueAllLots?.toFixed(2)}
                readOnly
                style={{ width: "100px", height: "30px" }}
              />
            </div>
            <div>
              <h2 style={{ fontSize: "14px", marginBottom: "5px" }}>
                Value per Lots
              </h2>
              <input
                type="number"
                value={valuePerLot?.toFixed(2)}
                readOnly
                style={{ width: "100px", height: "30px" }}
              />
            </div>
          </div>
          <div className="frame-13814">
            <div className="frame-13797">
              <input
                type="checkbox"
                id="yourCheckboxId"
                checked={isCheckedPortfolio}
                onChange={handleCheckboxChangePortfolio}
                style={{ cursor: "pointer" }}
              />
            </div>

            <div className="positional-portfolio">Positional Portfolio</div>
          </div>
          <div className="frame-13813">
            <div className="frame-13796">
              <input
                type="checkbox"
                id="yourCheckboxId"
                checked={isChecked}
                onChange={handleCheckboxChange}
                style={{ cursor: "pointer" }}
              />
            </div>
            <div className="buy-trades-first">Buy Trades First</div>
          </div>
          <div className="frame-13812">
            <div className="frame-137892">
              <input
                type="checkbox"
                id="yourCheckboxId"
                style={{ cursor: "pointer" }}
              />
            </div>
            <div className="tgt-sl-entry-on-per-lot-basis">
              Tgt / SL / Entry on Per Lot Basis
            </div>
          </div>
        </div>
        <div className="ellipse-47"></div>
        <div className="ellipse-48"></div>
        {/* <div className="group-13286"> */}
        <div
          className="rectangle-1557"
          style={{
            cursor:
              savingPortfolio || !IsPortfolioEdited ? "not-allowed" : "pointer",
          }}
        >
          {savingPortfolio ? (
            <div className="saveLoader">
              <RotatingLines
                visible={true}
                height="40"
                width="40"
                strokeColor="#4661BD"
                strokeWidth="5"
                animationDuration="0.75"
                ariaLabel="rotating-lines-loading"
                wrapperStyle={{}}
                wrapperClass=""
              />
            </div>
          ) : (
            <></>
          )}

          {isCheckIconVisible ? (
            <div className="CheckIconContainer">
              <AnimatedCheckIcon isVisible={isCheckIconVisible} />
            </div>
          ) : (
            <></>
          )}
        </div>

        {savingPortfolio || isCheckIconVisible ? (
          <></>
        ) : (
          <>
            <div
              className="ellipse-49"
              style={{
                cursor:
                  savingPortfolio || !IsPortfolioEdited
                    ? "not-allowed"
                    : "pointer",
              }}
            ></div>{" "}
            <img
              className="ellipse-50"
              src="/src/assets/Ellipse 50.png"
              style={{
                cursor:
                  savingPortfolio || !IsPortfolioEdited
                    ? "not-allowed"
                    : "pointer",
              }}
              onClick={() => {
                if (!savingPortfolio) {
                  setsavingPortfolio(true);
                  setclickedSave(true);
                  handleSavePortfolio();
                }
              }}
            />
          </>
        )}

        <div
          className="save-portfolio"
          style={{
            cursor:
              savingPortfolio || isCheckIconVisible || !IsPortfolioEdited
                ? "not-allowed"
                : "pointer",
            color:
              savingPortfolio || isCheckIconVisible || !IsPortfolioEdited
                ? "grey"
                : "black",
          }}
          onClick={() => {
            if (!savingPortfolio) {
              setsavingPortfolio(true);
              setclickedSave(true);
              handleSavePortfolio();
            }
          }}
        >
          Save Portfolio
        </div>
        <Modal
          isOpen={showModal}
          onRequestClose={() => setShowModal(false)}
          contentLabel="Confirm Save Modal"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 1000,
            },
            content: {
              width: getModalWidth(message),
              height: getModalHeight(message),
              margin: "auto",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              background: "white",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
              padding: "20px",
            },
          }}
        >
          <p
            style={{
              textAlign: "center",
              fontSize: "18px",
              marginBottom: "20px",
              wordWrap: "break-word",
            }}
          >
            {message}
          </p>
          <div className="modal-buttons" style={{ marginBottom: "20px", display: "flex", gap: "10px" }}>
            <button
              style={{
                padding: "8px 16px",
                borderRadius: "5px",
                backgroundColor: "#5cb85c",
                color: "white",
                border: "none",
                cursor: "pointer",
              }}
              onClick={() => setShowModal(false)}
            >
              OK
            </button>
            {message === "Please save the changes" && (
              <button
                style={{
                  padding: "8px 16px",
                  borderRadius: "5px",
                  backgroundColor: "#d9534f",
                  color: "white",
                  border: "none",
                  cursor: "pointer",
                }}
                onClick={() => {
                  setShowModal(false);
                  navigate("/F&O/Portfolio");
                }}
              >
                Cancel
              </button>
            )}
          </div>
        </Modal>

        <Modal
          isOpen={showModelForReset}
          onRequestClose={() => setShowmodalForReset(false)}
          contentLabel="Confirm Reset Modal"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 1000,
            },
            content: {
              width: "300px",
              height: "150px",
              margin: "auto",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              background: "white",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
              padding: "20px",
            },
          }}
        >
          <p
            style={{
              textAlign: "center",
              fontSize: "18px",
              marginBottom: "20px",
            }}
          >
            Do you really want to reset?
          </p>
          <div style={{ display: "flex", gap: "10px" }}>
            <button
              style={{
                padding: "8px 16px",
                borderRadius: "5px",
                backgroundColor: "#5cb85c",
                color: "white",
                border: "none",
                cursor: "pointer",
              }}
              onClick={() => {
                resetTheFields();
                setShowmodalForReset(false);
              }}
            >
              OK
            </button>
            <button
              style={{
                padding: "8px 16px",
                borderRadius: "5px",
                backgroundColor: "#d9534f",
                color: "white",
                border: "none",
                cursor: "pointer",
              }}
              onClick={() => setShowmodalForReset(false)}
            >
              Cancel
            </button>
          </div>
        </Modal>
        {/* <div className="group-13285"> */}
        <div
          className="container"
          onClick={() => setShowmodalForReset(true)}
          style={{ cursor: "pointer" }}
        >
          <div className="rectangle-1556"></div>
          <div className="ellipse-502"></div>

          <img className="ellipse-51" src="/src/assets/Ellipse 51.png" />

          <div className="reset">Reset</div>
        </div>

        <div
          className="rectangle-1555"
          style={{ cursor: "pointer" }}
          onClick={handleRefreshLtp}
        ></div>
        {!loading && (
          <div
            className="ellipse-512"
            style={{ cursor: "pointer" }}
            onClick={handleRefreshLtp}
          ></div>
        )}
        {!loading && (
          <img
            onClick={handleRefreshLtp}
            className="ellipse-52"
            src="/src/assets/Ellipse 52.png"
            style={{ cursor: "pointer" }}
          />
        )}
        {loading && (
          <div
            className="ellips-52"
            style={{
              width: "30px",
              height: "30px",
              position: " absolute",
              left: "1440px",
              top: "84px",
            }}
          >
            <RotatingLines
              visible={true}
              height="30"
              width="30"
              color="blue"
              strokeColor="#4661BD"
              strokeWidth="5"
              animationDuration="0.75"
              ariaLabel="rotating-lines-loading"
              wrapperStyle={{}}
              wrapperClass=""
            />
          </div>
        )}
        <div
          className="refresh"
          style={{ cursor: "pointer" }}
          onClick={handleRefreshLtp}
        >
          Refresh
        </div>
        <Fandorow
          dateOptions={dateOptions}
          ref={FandRowRef}
          setlegsEdited={setlegsEdited}
          editPortfolio={editPortfolio}
          portfolio={portfolio}
          selectedDate={selectedDate}
          stock_symbol={stock_symbol}
          setIsPortfolioEdited={setIsPortfolioEdited}
          isPortfolioExecuted={isPortfolioExecuted}
          setgotAllLTP={setgotAllLTP}
          setmargin={setmargin}
          order_type={order_type}
          isCheckedPortfolio={isCheckedPortfolio}
          predefinedStrategy={predefinedStrategy}
        />
        <div className="toggle1">
          <div className="toggle-switch-container1">
            <div
              className={`toggle-switch1 ${activeTab === "execution" ? "active" : ""
                }`}
              onClick={() => handleTabClick("execution")}
            >
              <span>Execution Parameters</span>
            </div>
            <div
              className={`toggle-switch1 ${activeTab === "target" ? "active" : ""
                }`}
              onClick={() => handleTabClick("target")}
            >
              <span>Target Settings</span>
            </div>
            <div
              className={`toggle-switch1 ${activeTab === "stoploss" ? "active" : ""
                }`}
              onClick={() => handleTabClick("stoploss")}
            >
              <span>Stoploss Settings</span>
            </div>
            <div
              className={`toggle-switch1 ${activeTab === "exit" ? "active" : ""
                }`}
              onClick={() => handleTabClick("exit")}
            >
              <span>Exit Settings</span>
            </div>
            <div
              className={`toggle-switch1 ${activeTab === "extra" ? "active" : ""
                }`}
              onClick={() => handleTabClick("extra")}
            >
              <span>Extra Conditions</span>
            </div>
            <div
              className={`toggle-switch1 ${activeTab === "other" ? "active" : ""
                }`}
              onClick={() => handleTabClick("other")}
            >
              <span>Other Settings</span>
            </div>
          </div>
        </div>
        {isExecutionTabActive && (
          <div className="execution-parameters">
            <div className="frame-13802">
              <div className="execution-settings">Execution Settings</div>
              <div className="timings">Timings</div> <br />
              <table className="product">
                <thead>
                  <tr>
                    <th>Product</th>
                    <th>Strategy Tag</th>
                    <th>If One or More Leg Falls</th>
                    <th>Legs Execution</th>
                    <th>Qty By Exposure</th>
                    <th>Max Lots</th>
                    <th>Premium Gap</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <select
                        disabled={isPortfolioExecuted || isCheckedPortfolio}
                        className="Product-dropdown Product-dropdown1"
                        onChange={(e) => {
                          setIsPortfolioEdited(true);
                          setProduct(e.target.value);
                        }}
                        style={{ cursor: "pointer" }}
                        value={
                          editPortfolio
                            ? portfolio.product_type
                            : isCheckedPortfolio
                              ? "NORMAL"
                              : product_type
                        }
                      >
                        <option value="" disabled>
                          Select
                        </option>
                        <option value="NORMAL">NRML</option>
                        <option value="MIS">MIS</option>
                      </select>
                    </td>

                    <td>
                      <select
                        disabled={isPortfolioExecuted}
                        className="Strategy-dropdown"
                        onChange={(e) => {
                          setIsPortfolioEdited(true);
                          setSelectedStrategy(e.target.value);
                        }}
                        style={{ cursor: "pointer" }}
                      >
                        <option selected disabled>
                          {" "}
                          Select
                        </option>
                        {strategyTags.map((tag, index) => (
                          <option
                            selected={
                              editPortfolio && portfolio.strategy === tag
                            }
                            key={index}
                            value={tag}
                          >
                            {tag}
                          </option>
                        ))}
                      </select>
                    </td>
                    <td>
                      <select
                        className="One-or-More-dropdown"
                        style={{ cursor: "pointer" }}
                      >
                        <option value="option1">Keep PLaced Legs</option>
                        <option value="option2">Keep PLaced Legs2</option>
                        <option value="option3">Keep PLaced Legs3</option>
                      </select>
                    </td>
                    <td>
                      <select
                        className="Legs-Execution-dropdown"
                        style={{ cursor: "pointer" }}
                      >
                        <option value="option1">Parallel</option>
                        <option value="option2">Parallel2</option>
                        <option value="option3">Parallel3</option>
                      </select>
                    </td>
                    <td>
                      <input
                        type="number"
                        style={{
                          border: "1px solid #ccc",
                          borderRadius: "5px",
                          padding: "8px",
                          width: "100px",
                        }}
                        onInput={(e) => {
                          e.target.value = e.target.value.replace(
                            /[^0-9]/g,
                            ""
                          );
                        }}
                        onKeyDown={(e) => {
                          if (
                            e.key === "e" ||
                            e.key === "E" ||
                            e.key === "+" ||
                            e.key === "-"
                          ) {
                            e.preventDefault();
                          }
                        }}
                      />
                    </td>
                    <td>
                      <input
                        type="number"
                        onInput={(e) => {
                          const value = e.target.value.replace(/[^0-9]/g, "");
                          setMaxLots(value === "" ? 0 : parseInt(value, 10));
                        }}
                        style={{
                          border: "1px solid #ccc",
                          borderRadius: "5px",
                          padding: "8px",
                          width: "100px",
                        }}
                        value={max_lots == 0 ? "" : max_lots}
                        onKeyDown={(e) => {
                          if (
                            e.key === "e" ||
                            e.key === "E" ||
                            e.key === "+" ||
                            e.key === "-"
                          ) {
                            e.preventDefault();
                          }
                        }}
                      />
                    </td>
                    <td>
                      <input
                        type="number"
                        onInput={(e) => {
                          e.target.value = e.target.value.replace(
                            /[^0-9]/g,
                            ""
                          );
                        }}
                        style={{
                          border: "1px solid #ccc",
                          borderRadius: "5px",
                          padding: "8px",
                          width: "100px",
                        }}
                        onKeyDown={(e) => {
                          if (
                            e.key === "e" ||
                            e.key === "E" ||
                            e.key === "+" ||
                            e.key === "-"
                          ) {
                            e.preventDefault();
                          }
                        }}
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
              <div className="Entry-Setting">Entry Setting</div>
              <div className="line1"></div> <br />
              <table className="product1">
                <thead>
                  <tr>
                    <th>Portfolio Execution Mode</th>
                    <th>Entry Order Type</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <select
                        className="Portfolio-Execution-dropdown"
                        style={{ cursor: "pointer" }}
                      >
                        <option value="option1">Manual</option>
                        <option value="option2">Manual2</option>
                        <option value="option3">Manual3</option>
                      </select>
                    </td>
                    <td>
                      <select
                        disabled={isPortfolioExecuted}
                        className="Entry-Order-dropdown"
                        onChange={(e) => {
                          setIsPortfolioEdited(true);
                          setEntryOrder(e.target.value);
                        }}
                        style={{ cursor: "pointer" }}
                      >
                        <option selected disabled>
                          Select
                        </option>
                        <option
                          value="MARKET"
                          selected={
                            editPortfolio && portfolio.order_type === "MARKET"
                          }
                        >
                          MARKET
                        </option>
                        <option
                          value="LIMIT"
                          selected={
                            editPortfolio && portfolio.order_type === "LIMIT"
                          }
                        >
                          LIMIT
                        </option>
                        <option
                          value="SL_LIMIT"
                          selected={
                            editPortfolio && portfolio.order_type === "SL_LIMIT"
                          }
                        >
                          SL_LIMIT
                        </option>
                      </select>
                    </td>
                  </tr>
                </tbody>
              </table>
              <table className="product2">
                <thead>
                  <tr>
                    <th>Run On Days</th>
                    <th>Start Time</th>
                    <th>End Time</th>
                    <th>SqOff Time</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <div className="timing-border">
                      <div className="custom-dropdown" ref={dropdownRef}>
                        <div style={{ display: "flex", alignItems: "center" }}>
                          <input
                            type="text"
                            className="dropdown-toggle"
                            value={
                              selectedWeekdays.length > 0
                                ? selectedWeekdays.join(", ")
                                : "Select weekdays"
                            }
                            onClick={toggleDropdown}
                            readOnly
                            style={{ cursor: "pointer", width: "160px" }}
                          />
                          <button
                            onClick={toggleDropdown}
                            style={{
                              marginLeft: "-20px",
                              border: "none",
                              color: "black",
                              backgroundColor: "transparent",
                              fontSize: "20px",
                              cursor: "pointer",
                            }}
                          >
                            {isDropdownOpen ? "▲" : "▼"}
                          </button>
                        </div>
                        {dropdownVisible && (
                          <div
                            className="dropdown-content"
                            style={{ cursor: "pointer", width: "85%" }}
                          >
                            <input
                              type="checkbox"
                              id="selectAll"
                              checked={selectAllChecked}
                              onChange={toggleSelectAll}
                              style={{ cursor: "pointer" }}
                            />
                            <label htmlFor="selectAll">
                              {selectAllChecked ? "Deselect All" : "Select All"}
                            </label>
                            {weekdays.map((weekday) => (
                              <div key={weekday}>
                                <input
                                  type="checkbox"
                                  id={weekday}
                                  value={weekday}
                                  checked={selectedWeekdays.includes(weekday)}
                                  onChange={() => toggleWeekday(weekday)}
                                  style={{ cursor: "pointer" }}
                                />
                                <label htmlFor={weekday}>{weekday}</label>
                              </div>
                            ))}
                            <div style={{ display: "flex" }}>
                              <button
                                className="weekdays-button1"
                                onClick={handleOk}
                                style={{ cursor: "pointer" }}
                              >
                                Ok
                              </button>
                              <button
                                className="weekdays-button2"
                                onClick={handleCancel}
                                style={{ cursor: "pointer" }}
                              >
                                Cancel
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <td>
                      <Timepic
                        label="startTime"
                        onTimeChange={handleTimeChange}
                        disabled={isPortfolioExecuted}
                        value={
                          editPortfolio ? portfolio.start_time : "09:20:00"
                        }
                      />
                    </td>
                    <td>
                      <Timepic
                        label="endTime"
                        onTimeChange={handleTimeChange}
                        disabled={isPortfolioExecuted}
                        value={editPortfolio ? portfolio.end_time : "15:00:00"}
                      />
                    </td>
                    <td style={{ padding: 0, position: "relative" }}>
                      {!isCheckedPortfolio ? (
                        <Timepic
                          label="sqOffTime"
                          onTimeChange={handleTimeChange}
                          disabled={isPortfolioExecuted}
                          value={
                            editPortfolio
                              ? portfolio.square_off_time
                              : "15:10:00"
                          }
                        />
                      ) : (
                        <input
                          type="text"
                          onChange={handleTimerChange}
                          style={{
                            textAlign: "center",
                            padding: "10px 0px 10px 0px",
                            border: "1px black solid",
                            width: "115px",
                          }}
                          onFocus={() => {
                            setIsFocused(true);
                          }}
                          onBlur={() => {
                            setIsFocused(false);
                          }}
                          disabled={isPortfolioExecuted}
                          value={sqOffTime === "00:00:00" ? "" : sqOffTime}
                        />
                      )}
                      {isFocused && (
                        <div style={tooltipStyle}>
                          {isCheckedPortfolio ? "DD MMM HH:MM:SS" : "HH:MM:SS"}
                        </div>
                      )}
                    </td>
                  </tr>
                </tbody>
              </table>
              {/* <table className="product3">
                <thead>
                  <tr>
                    <th>Estimated Margin</th>
                  </tr>
                </thead>
                <tbody>
                  <td>Click to Refresh</td>
                </tbody>
              </table> */}
            </div>
          </div>
        )}
        {isTargetTabActive && (
          <div className="execution-parameters">
            <div className="frame-13802">
              <div className="Portfolio-Profit-Protection">
                Portfolio Profit Protection
              </div>
              <table className="TargetSettings">
                <thead>
                  <tr>
                    <th>Target Type</th>
                    <th style={{ paddingRight: "9.6rem" }}>
                      If Profit Reaches
                    </th>
                    <th>Lock Minimum Profit At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <select
                        className="Product-dropdown"
                        style={{ marginRight: "10rem", cursor: "pointer" }}
                      >
                        <option value="option1">Combined Profit</option>
                        <option value="option2">Combined Profit2</option>
                        <option value="option3">Combined Profit3</option>
                      </select>
                    </td>
                    <td>
                      <input type="text" value={0} />
                    </td>
                    <td>
                      <input type="text" value={0} />
                    </td>
                  </tr>
                </tbody>
              </table>
              <table className="TargetSettings1">
                <thead>
                  <tr>
                    <th>Combined Profit</th>
                    <th>For Every Icrease In Profit By</th>
                    <th>Trail Profit By</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <input
                        type="text"
                        style={{
                          border: "1px solid #ccc",
                          borderRadius: "5px",
                          padding: "8px",
                        }}
                      />
                    </td>
                    <td>
                      <input type="text" value={0} />
                    </td>
                    <td>
                      <input type="text" value={0} style={{ width: "185px" }} />
                    </td>
                  </tr>
                </tbody>
              </table>
              <table className="TargetSettings2">
                <thead>
                  <tr>
                    <th>Combined Profit</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <select
                        className="Product-dropdown"
                        style={{ marginRight: "10rem", cursor: "pointer" }}
                      >
                        <option value="option1">None</option>
                        <option value="option2">None2</option>
                        <option value="option3">None3</option>
                      </select>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        )}
        {isStoplossTabActive && (
          <div className="execution-parameters">
            <div
              className="frame-13802"
              style={{ display: "flex", justifyContent: "space-between" }}
            >
              {/* Column 1 */}
              <div style={{ flex: 1, marginRight: "1rem" }}>
                {/* Checkbox 1 */}
                {/* <div style={{ display: "flex", alignItems: "center", marginBottom: "1rem" }}>
                  <input type="checkbox" style={{ marginRight: "0.5rem" }} />
                  <span style={{ color: "#C71A8A" }}>On SI SqOff only Loss Making Legs</span>
                </div> */}

                {/* Dropdown 1 */}
                <label style={{ color: "blue" }}> StopLoss Gap Type</label>
                <div style={{ marginBottom: "1rem" }}>
                  <select
                    className="Product-dropdown"
                    style={{ cursor: "pointer", width: "160px" }}
                  >
                    <option value="option1">Combined Premium</option>
                    <option value="option2">Combined Premium2</option>
                    <option value="option3">Combined Premium3</option>
                  </select>
                </div>

                {/* Input Field 1 */}
                <label style={{ color: "blue" }}> Combined Premium</label>
                <div style={{ marginBottom: "1rem" }}>
                  <input
                    type="text"
                    style={{
                      width: "100%",
                      border: "1px solid #ccc",
                      borderRadius: "5px",
                      padding: "8px",
                    }}
                  />
                </div>
              </div>

              {/* Column 2 */}
              <div style={{ flex: 1, marginLeft: "60px" }}>
                {/* Input Field 2 */}
                <label style={{ color: "blue" }}> SI Wait Seconds</label>
                <div style={{ marginBottom: "1rem" }}>
                  <input
                    type="text"
                    style={{
                      width: "100%",
                      border: "1px solid #ccc",
                      borderRadius: "5px",
                      padding: "8px",
                    }}
                  />
                </div>

                {/* Dropdown 2 */}
                <label style={{ color: "blue" }}> On Stopless</label>
                <div style={{ marginBottom: "1rem" }}>
                  <select
                    className="Product-dropdown"
                    style={{ cursor: "pointer", width: "150px" }}
                  >
                    <option value="option1">None</option>
                    <option value="option2">None2</option>
                    <option value="option3">None3</option>
                  </select>
                </div>
              </div>

              <div style={{ display: "flex", marginLeft: "60px" }}>
                <fieldset
                  style={{
                    width: "470px",
                    padding: "5px",
                    borderRadius: "5px",
                    height: "250px",
                    boxSizing: "border-box",
                    fontFamily: "Arial, sans-serif",
                  }}
                >
                  <legend>Move SL to Cost Settings</legend>

                  <div style={{ display: "flex", alignItems: "center" }}>
                    <input type="checkbox" />
                    <label
                      style={{
                        fontWeight: "bold",
                        border: "2px dotted",
                        marginLeft: "4px",
                      }}
                    >
                      Move SL to Cost
                    </label>
                    <label style={{ marginLeft: "10px", color: "#4FBB80" }}>
                      {" "}
                      MoveSL safety Seconds{" "}
                    </label>
                    <input
                      type="number"
                      onInput={(e) => {
                        e.target.value = e.target.value.replace(/[eE+\-]/g, "");
                      }}
                      style={{
                        width: "80px",
                        marginLeft: "4px",
                        padding: "5px",
                      }}
                    />
                  </div>

                  <div style={{ marginLeft: "30px", marginTop: "10px" }}>
                    <lable style={{ color: "#4FBB80" }}> Move SL Action</lable>
                    <select
                      className="Product-dropdown"
                      style={{ cursor: "pointer", width: "80%" }}
                    >
                      <option value="option1">
                        Move Only for Profitable Legs
                      </option>
                      <option value="option2">
                        Move SL for All Legs Despite Loss/Profit
                      </option>
                      <option value="option3">
                        Move SL to LTP + Buffer for Loss Making Legs
                      </option>
                    </select>
                  </div>
                  <div style={{ display: "flex" }}>
                    <input type="checkbox" />
                    <label> Trail SL only after Move SL to Cost</label>
                  </div>
                  <div style={{ display: "flex", color: "#4FBB80" }}>
                    <input type="checkbox" />
                    <label> No Move SL to Cost for BUY Legs</label>
                  </div>
                  <div style={{ display: "flex" }}>
                    <input type="checkbox" />
                    <label>
                      {" "}
                      Perform Move SL Every time whatnever SL hits on a Leg
                    </label>
                  </div>
                  <div style={{ display: "flex" }}>
                    <input type="checkbox" />
                    <label>
                      {" "}
                      Perform Move SL Every time whatnever TGT hits on a Leg{" "}
                    </label>{" "}
                    <label style={{ color: "red", fontWeight: "bold" }}>
                      *
                    </label>
                  </div>
                </fieldset>
              </div>
            </div>
          </div>
        )}

        {isExitTabActive && (
          <div className="execution-parameters">
            <div className="frame-13802">
              <table className="ExitSettings">
                <thead>
                  <tr>
                    <h4>Combined Premium</h4>
                  </tr>
                </thead>
              </table>
              <br />
              <table className="ExitSettings1">
                <label>
                  <input type="checkbox" />
                  On Sl SqOff only Profit Making Legs
                </label>
                <label>
                  <input type="checkbox" />
                  Slice Exit Orders as Entry Orders
                </label>
                <label style={{ color: "#9B0101" }}>
                  <input type="checkbox" />
                  Stop Exit Orders If No Positions Exists
                </label>
              </table>
              <label className="h4">
                <h4>On Portfolio Complete</h4>
                <span className="notApplicable">
                  (Not Applicable on Manual)
                </span>
                <select
                  className="Product-dropdown"
                  style={{ marginRight: "11rem", cursor: "pointer" }}
                >
                  <option value="option1">None</option>
                  <option value="option2">None2</option>
                  <option value="option3">None3</option>
                </select>
              </label>
              <label className="OrderRetry">
                <span className="OrderRetry1">Order Retry Settings</span>
                <br />
                <br />
                <span className="Settings">
                  These Settings will be applicable When an Order gets Rejected.
                </span>
                <br />
                <span className="Settings1">
                  First, Stoxxo will apply the Strategy Tag Retry settings as
                  per <br />
                  Entry / Exit Order. This will behave like a Failover Setting{" "}
                  <br />
                  Which will be applied after Strategy tag settings.
                </span>
                <br />
                <h4>Wait Between Each Retry (Sec)</h4>
                <h4>Max Wait Time (Sec)</h4>
              </label>
            </div>
          </div>
        )}
        {isExtraConditions && (
          <div
            style={{ marginLeft: "30px", display: "flex", marginTop: "10px" }}
          >
            <div style={{ width: "100%" }}>
              <fieldset
                style={{
                  padding: "5px",
                  borderRadius: "5px",
                  height: "225px",
                  boxSizing: "border-box",
                }}
                htmlFor=""
              >
                <legend>Underlying Gap-Up/Gap-Down</legend>
                <div>
                  <label style={{ fontSize: "16px" }}>
                    <input
                      type="checkbox"
                      checked={isExtraConditionsChecked}
                      onChange={() =>
                        setisExtraConditionsChecked(!isExtraConditionsChecked)
                      }
                    />
                    Gap-Up / Gap-Down Conditions
                  </label>
                </div>
                <div
                  style={{
                    border: "1px solid gray",
                    height: "165px",
                    padding: "10px",
                  }}
                >
                  <div style={{ display: "flex", flexWrap: "wrap" }}>
                    <div style={{ flex: "1 1 50%", padding: "5px" }}>
                      <label>Min Gap-Up</label>
                      <input
                        type="text"
                        placeholder=""
                        style={{
                          width: "100%",
                          height: "30px",
                          borderRadius: "5px",
                          border: "1px solid gray",
                          padding: "10px",
                        }}
                        disabled={isExtraConditionsChecked}
                      />
                    </div>
                    <div style={{ flex: "1 1 50%", padding: "5px" }}>
                      <label>Max Gap-Up</label>
                      <input
                        type="text"
                        placeholder=""
                        disabled={isExtraConditionsChecked}
                        style={{
                          width: "100%",
                          height: "30px",
                          borderRadius: "5px",
                          border: "1px solid gray",
                          padding: "10px",
                        }}
                      />
                    </div>

                    <div style={{ flex: "1 1 50%", padding: "5px" }}>
                      <label>Min Gap-Down</label>
                      <input
                        type="text"
                        placeholder=""
                        disabled={isExtraConditionsChecked}
                        style={{
                          width: "100%",
                          height: "30px",
                          borderRadius: "5px",
                          border: "1px solid gray",
                          padding: "10px",
                        }}
                      />
                    </div>
                    <div style={{ flex: "1 1 50%", padding: "5px" }}>
                      <label>Max Gap-Down</label>
                      <input
                        type="text"
                        placeholder=""
                        disabled={isExtraConditionsChecked}
                        style={{
                          width: "100%",
                          height: "30px",
                          borderRadius: "5px",
                          border: "1px solid gray",
                          padding: "10px",
                        }}
                      />
                    </div>
                  </div>
                </div>
              </fieldset>
            </div>

            <div style={{ width: "85%" }}>
              <fieldset
                style={{
                  padding: "5px",
                  borderRadius: "5px",
                  height: "110px",
                  boxSizing: "border-box",
                  marginLeft: "15px",
                }}
                htmlFor=""
              >
                <legend>Underlying Day Open Settings</legend>
                <div style={{ display: "flex" }}>
                  <label style={{ color: "blue", fontSize: "16px" }} htmlFor="">
                    Day Open Condition
                  </label>
                  <span
                    style={{
                      color: "red",
                      fontSize: "18px",
                      marginLeft: "5px",
                      marginTop: "7px",
                    }}
                  >
                    *
                  </span>
                </div>
                <div>
                  <select
                    name=""
                    id=""
                    style={{
                      width: "100%",
                      height: "30px",
                      borderRadius: "5px",
                      border: "1px solid gray",
                    }}
                  >
                    <option value="">None</option>
                  </select>
                </div>
              </fieldset>

              <fieldset
                style={{
                  padding: "5px",
                  borderRadius: "5px",
                  height: "105px",
                  boxSizing: "border-box",
                  marginLeft: "15px",
                  marginTop: "10px",
                }}
                htmlFor=""
              >
                <legend>Combined Wait & Trade Settings</legend>
                <div style={{ display: "flex" }}>
                  <label style={{ color: "blue", fontSize: "16px" }} htmlFor="">
                    Wait & Trade Value
                  </label>
                </div>
                <div>
                  <input
                    type="text"
                    placeholder=""
                    style={{
                      width: "100%",
                      height: "30px",
                      borderRadius: "5px",
                      border: "1px solid gray",
                      padding: "10px",
                    }}
                  />
                </div>
              </fieldset>
            </div>

            <div style={{ width: "120%", marginLeft: "10px" }}>
              <fieldset
                style={{
                  padding: "5px",
                  borderRadius: "5px",
                  height: "225px",
                  boxSizing: "border-box",
                }}
              >
                <legend>Leg Settings</legend>
                <div style={{ display: "flex" }}>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      marginRight: "20px",
                    }}
                  >
                    <label
                      style={{ color: "blue", fontSize: "16px" }}
                      htmlFor=""
                    >
                      Day Open Condition
                    </label>
                    <span style={{ color: "red", fontSize: "16px" }}>*</span>
                  </div>
                  <div style={{ display: "flex" }}>
                    <label
                      style={{ color: "blue", fontSize: "16px" }}
                      htmlFor=""
                    >
                      If Condition Fails
                    </label>
                  </div>
                </div>
                <div style={{ display: "flex", marginTop: "10px" }}>
                  <input
                    type="text"
                    placeholder=""
                    style={{
                      width: "50%",
                      height: "30px",
                      borderRadius: "5px",
                      border: "1px solid gray",
                      padding: "10px",
                    }}
                  />
                  <select
                    type="text"
                    placeholder=""
                    style={{
                      width: "50%",
                      height: "30px",
                      borderRadius: "5px",
                      border: "1px solid gray",
                      padding: "10px",
                      marginLeft: "5px",
                    }}
                  >
                    <option value=""></option>
                  </select>
                </div>
                <div>
                  <fieldset
                    style={{
                      padding: "5px",
                      borderRadius: "5px",
                      height: "125px",
                      boxSizing: "border-box",
                    }}
                    htmlFor=""
                  >
                    <legend>Legs's Current Day Change</legend>
                    <div>
                      <label htmlFor="" style={{ marginTop: "-5px" }}>
                        Change Type
                      </label>
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          marginTop: "-5px",
                        }}
                      >
                        <div
                          style={{
                            marginRight: "20px",
                            display: "flex",
                            alignItems: "center",
                          }}
                        >
                          <input
                            type="radio"
                            id="type1"
                            name="changeType"
                            value="Type 1"
                            checked={selectedType === "Type 1"}
                            onChange={() => setSelectedType("Type 1")}
                            style={{
                              appearance: "none",
                              width: "20px",
                              height: "20px",
                              border: "2px solid green",
                              borderRadius: "50%",
                              outline: "none",
                              cursor: "pointer",
                              marginRight: "5px",
                              position: "relative",
                              backgroundColor:
                                selectedType === "Type 1" ? "green" : "white",
                            }}
                          />
                          <label htmlFor="type1" style={{ color: "green" }}>
                            Positive
                          </label>
                        </div>
                        <div style={{ display: "flex", alignItems: "center" }}>
                          <input
                            type="radio"
                            id="type2"
                            name="changeType"
                            value="Type 2"
                            checked={selectedType === "Type 2"}
                            onChange={() => setSelectedType("Type 2")}
                            style={{
                              appearance: "none",
                              width: "20px",
                              height: "20px",
                              border: "2px solid red",
                              borderRadius: "50%",
                              outline: "none",
                              cursor: "pointer",
                              marginRight: "5px",
                              position: "relative",
                              backgroundColor:
                                selectedType === "Type 2" ? "red" : "white",
                            }}
                          />
                          <label htmlFor="type2" style={{ color: "red" }}>
                            Negative
                          </label>
                        </div>
                      </div>
                    </div>
                  </fieldset>
                </div>
                <div style={{ display: "flex", marginTop: "-65px" }}>
                  <div style={{ flex: "1 1 50%", padding: "5px" }}>
                    <label>Min Gap-Down</label>
                    <input
                      type="text"
                      placeholder=""
                      style={{
                        width: "100%",
                        height: "25px",
                        borderRadius: "5px",
                        border: "1px solid gray",
                        padding: "10px",
                      }}
                    />
                  </div>
                  <div style={{ flex: "1 1 50%", padding: "5px" }}>
                    <label>Max Gap-Down</label>
                    <input
                      type="text"
                      placeholder=""
                      style={{
                        width: "100%",
                        height: "25px",
                        borderRadius: "5px",
                        border: "1px solid gray",
                        padding: "10px",
                      }}
                    />
                  </div>
                </div>
              </fieldset>
            </div>

            <div style={{ width: "105%", marginLeft: "10px" }}>
              <fieldset
                style={{
                  padding: "5px",
                  borderRadius: "5px",
                  height: "225px",
                  boxSizing: "border-box",
                }}
                htmlFor=""
              >
                <legend>Leg's Wait & Trade Settings</legend>
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    marginRight: "20px",
                  }}
                >
                  <label style={{ color: "blue", fontSize: "16px" }} htmlFor="">
                    Wait Trade Monitoring
                  </label>
                </div>
                <div style={{ display: "flex" }}>
                  <select
                    type="text"
                    placeholder=""
                    style={{
                      width: "100%",
                      height: "30px",
                      borderRadius: "5px",
                      border: "1px solid gray",
                      marginLeft: "5px",
                    }}
                    value={selectedOptionminuteclose}
                    onChange={(e) =>
                      setSelectedOptionminuteclose(e.target.value)
                    }
                  >
                    <option value="real-time">Real Time</option>
                    <option value="minute-close">Minute Close</option>
                    <option value="interval">Interval</option>
                  </select>

                  {selectedOptionminuteclose === "minute-close" && (
                    <div
                      style={{
                        display: "inline-block",
                        marginLeft: "20px",
                        textAlign: "left",
                        width: "100%",
                        marginTop: "-27px",
                      }}
                    >
                      <label style={{ display: "block" }}>
                        Minute Interval
                      </label>
                      <input
                        type="text"
                        style={{
                          height: "30px",
                          borderRadius: "5px",
                          border: "1px solid gray",
                          padding: "5px",
                          width: "100%",
                        }}
                      />
                    </div>
                  )}
                  {selectedOptionminuteclose === "interval" && (
                    <div
                      style={{
                        display: "inline-block",
                        marginLeft: "20px",
                        textAlign: "left",
                        width: "100%",
                        marginTop: "-27px",
                      }}
                    >
                      <label style={{ display: "block" }}>
                        seconds Interval
                      </label>
                      <input
                        type="text"
                        style={{
                          height: "30px",
                          borderRadius: "5px",
                          border: "1px solid gray",
                          padding: "5px",
                          width: "100%",
                        }}
                      />
                    </div>
                  )}
                </div>

                <div style={{ display: "flex" }}>
                  <input
                    type="checkbox"
                    checked={isCheckedcheck}
                    onChange={() => setIsCheckedcheck(!isCheckedcheck)}
                  />
                  <label style={{ fontSize: "16px" }}>
                    {" "}
                    If any leg Hits W&T,then execute other legs at W&T Price of
                    Execute if the price goes below the adjusted Price of Time
                    Hit.
                  </label>
                </div>
                {isCheckedcheck && (
                  <div style={{ display: "flex", marginTop: "-10px" }}>
                    <div style={{ flex: "1 1 50%", padding: "0px" }}>
                      <label> Force Price adjust</label>
                      <input
                        type="text"
                        placeholder=""
                        style={{
                          width: "100%",
                          height: "25px",
                          borderRadius: "5px",
                          border: "1px solid gray",
                          padding: "10px",
                        }}
                      />
                    </div>
                    <div
                      style={{
                        flex: "1 1 50%",
                        padding: "0px",
                        marginLeft: "20px",
                      }}
                    >
                      <label>force Execute time</label>
                      <input
                        type="text"
                        placeholder=""
                        style={{
                          width: "100%",
                          height: "25px",
                          borderRadius: "5px",
                          border: "1px solid gray",
                          padding: "10px",
                        }}
                      />
                    </div>
                  </div>
                )}
              </fieldset>
            </div>
            <div style={{ width: "105%" }}>
              <fieldset
                style={{
                  padding: "5px",
                  borderRadius: "5px",
                  height: "105px",
                  boxSizing: "border-box",
                  marginLeft: "15px",
                }}
                htmlFor=""
              >
                <legend>All CE legs Combined P&L(Rupees only)</legend>
                <div style={{ display: "flex" }}>
                  <div style={{ flex: "1 1 50%", padding: "5px" }}>
                    <label>Max Profit</label>
                    <input
                      type="text"
                      placeholder=""
                      style={{
                        width: "100%",
                        height: "30px",
                        borderRadius: "5px",
                        border: "1px solid gray",
                        padding: "10px",
                      }}
                    />
                  </div>
                  <div style={{ flex: "1 1 50%", padding: "5px" }}>
                    <label>Max Loss</label>
                    <input
                      type="text"
                      placeholder=""
                      style={{
                        width: "100%",
                        height: "30px",
                        borderRadius: "5px",
                        border: "1px solid gray",
                        padding: "10px",
                      }}
                    />
                  </div>
                </div>
              </fieldset>
              <div style={{ width: "100%" }}>
                <fieldset
                  style={{
                    padding: "5px",
                    borderRadius: "5px",
                    height: "105px",
                    boxSizing: "border-box",
                    marginLeft: "15px",
                    marginTop: "10px",
                  }}
                  htmlFor=""
                >
                  <legend>All PE legs Combined P&L(Rupees only)</legend>
                  <div style={{ display: "flex" }}>
                    <div style={{ flex: "1 1 50%", padding: "5px" }}>
                      <label>Max Profit</label>
                      <input
                        type="text"
                        placeholder=""
                        style={{
                          width: "100%",
                          height: "30px",
                          borderRadius: "5px",
                          border: "1px solid gray",
                          padding: "10px",
                        }}
                      />
                    </div>
                    <div style={{ flex: "1 1 50%", padding: "5px" }}>
                      <label>Max Loss</label>
                      <input
                        type="text"
                        placeholder=""
                        style={{
                          width: "100%",
                          height: "30px",
                          borderRadius: "5px",
                          border: "1px solid gray",
                          padding: "10px",
                        }}
                      />
                    </div>
                  </div>
                </fieldset>
              </div>
            </div>
          </div>
        )}
        {isOtherTabActive && (
          <div className="execution-parameters">
            <div className="frame-other">
              <div
                style={{
                  padding: "5px",
                  borderRadius: "10px",
                }}
              >
                <div style={{ gap: "25px" }} className="apperencemain">
                  <div>
                    <div className="containerone">
                      <fieldset
                        style={{
                          maxwidth: "100%",
                          padding: "5px",
                          borderRadius: "5px",
                          maxHeight: "100%",
                          boxSizing: "border-box",
                          fontFamily: "Arial, sans-serif",
                        }}
                      >
                        <legend
                          style={{
                            padding: "4px ",
                            fontSize: "18px",
                            lineHeight: "21px",
                            color: "#000",
                            fontWeight: "600",
                            fontFamily: "Roboto",
                            display: "block",
                          }}
                        >
                          Execute / ReExecute Delays for Legs
                        </legend>

                        <div className="rowall">
                          <div className="columone">
                            <label>In Seconds </label>
                            <input
                              type="text"
                              style={{
                                width: "120px",
                                borderRadius: "0px",
                                padding: "3px 0px",
                                border: "none",
                                borderBottom: "1px solid #000",
                              }}
                            />
                          </div>
                          <div className="columnone">
                            <label>In Candle Minutes </label>
                            <input
                              type="password"
                              style={{
                                width: "120px",
                                padding: "3px 0px",
                                border: "1px solid #000",
                                borderRadius: "3px",
                              }}
                            />
                          </div>
                        </div>
                      </fieldset>
                    </div>

                    <div className="containerone">
                      <fieldset
                        style={{
                          maxwidth: "100%",
                          padding: "5px",
                          borderRadius: "5px",
                          maxHeight: "100%",
                          boxSizing: "border-box",
                          fontFamily: "Arial, sans-serif",
                        }}
                      >
                        <legend
                          style={{
                            padding: "4px ",
                            fontSize: "18px",
                            lineHeight: "21px",
                            color: "#000",
                            fontWeight: "600",
                            fontFamily: "Roboto",
                            display: "block",
                          }}
                        >
                          Execute / ReExecute Delays for Portfolio
                        </legend>

                        <div className="rowall">
                          <div className="columone">
                            <label style={{ color: "#4661bd" }}>
                              In Seconds{" "}
                            </label>
                            <input
                              type="text"
                              style={{
                                width: "120px",
                                borderRadius: "0px",
                                padding: "3px 0px",
                                border: "none",
                                borderBottom: "1px solid #000",
                              }}
                            />
                          </div>
                          <div className="columnone">
                            <label style={{ color: "#4661bd" }}>
                              In Candle Minutes{" "}
                            </label>
                            <input
                              type="password"
                              style={{
                                width: "120px",
                                padding: "3px 0px",
                                border: "1px solid #000",
                                borderRadius: "3px",
                              }}
                            />
                          </div>
                        </div>
                      </fieldset>
                    </div>
                  </div>
                  <div style={{ display: "block", marginTop: "1%" }}>
                    <div style={{ margin: "10px 0px" }}>
                      <label
                        style={{
                          padding: "5px 0px",
                          fontSize: "15px",
                          lineHeight: "21px",
                          color: "#4661bd",
                          fontWeight: "500",
                          fontFamily: "Roboto",
                        }}
                      >
                        Straddler Width Multiplier
                      </label>
                      <input
                        type="text"
                        style={{
                          width: "190px",
                          borderRadius: "0px",
                          padding: "5px 0px",
                          border: "none",
                          borderBottom: "1px solid #000",
                        }}
                      />
                    </div>
                    <div style={{ marginTop: "5%" }}>
                      <label
                        style={{
                          padding: "5px 0px",
                          fontSize: "15px",
                          lineHeight: "21px",
                          color: "#4661bd",
                          fontWeight: "500",
                          fontFamily: "Roboto",
                        }}
                      >
                        Delay Between Legs in sec.
                      </label>
                      <input
                        type="text"
                        style={{
                          width: "190px",
                          padding: "5px 0px",
                          border: "1px solid #000",
                          borderRadius: "3px",
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div style={{ margin: "10px 0px" }}>
                      <label
                        style={{
                          padding: "5px 0px",
                          fontSize: "15px",
                          lineHeight: "21px",
                          color: "#4661bd",
                          fontWeight: "500",
                          fontFamily: "Roboto",
                        }}
                      >
                        On Target Action On
                      </label>
                      <select
                        style={{
                          width: "190px",
                          padding: "5px 0px",
                          border: "1px solid #000",
                          borderRadius: "3px",
                        }}
                      >
                        <option>On Target Only</option>
                      </select>
                    </div>
                    <div style={{ margin: "10px 0px" }}>
                      <label
                        style={{
                          padding: "5px 0px",
                          fontSize: "15px",
                          lineHeight: "21px",
                          color: "#4661bd",
                          fontWeight: "500",
                          fontFamily: "Roboto",
                        }}
                      >
                        On SL Action on
                      </label>
                      <select
                        style={{
                          width: "190px",
                          padding: "5px 0px",
                          border: "1px solid #000",
                          borderRadius: "3px",
                        }}
                      >
                        <option>On Target Only</option>
                      </select>
                    </div>
                    <div style={{ margin: "10px 0px" }}>
                      <label
                        style={{
                          padding: "5px 0px",
                          fontSize: "15px",
                          lineHeight: "21px",
                          color: "#4661bd",
                          fontWeight: "500",
                          fontFamily: "Roboto",
                        }}
                      >
                        Portfolio ReExecute Count
                      </label>
                      <input
                        type="number"
                        onInput={(e) => {
                          e.target.value = e.target.value.replace(
                            /[^0-9]/g,
                            ""
                          );
                        }}
                        style={{
                          width: "190px",
                          padding: "5px 0px",
                          border: "1px solid #000",
                          borderRadius: "3px",
                        }}
                      />
                    </div>
                  </div>
                  <div style={{ display: "block", width: "250px" }}>
                    <div style={{ margin: "10px 0px" }}>
                      <label
                        style={{
                          padding: "5px 0px",
                          fontSize: "15px",
                          lineHeight: "21px",
                          color: "#4661bd",
                          fontWeight: "500",
                          fontFamily: "Roboto",
                        }}
                      >
                        Portfolio ReExecution Saftey Seconds
                      </label>
                      <input
                        type="text"
                        style={{
                          width: "190px",
                          padding: "5px 0px",
                          border: "1px solid #000",
                          borderRadius: "3px",
                        }}
                      />
                    </div>
                    <div style={{ margin: "10px 0px" }}>
                      <label
                        style={{
                          padding: "5px 0px",
                          fontSize: "15px",
                          lineHeight: "21px",
                          color: "#4661bd",
                          fontWeight: "500",
                          fontFamily: "Roboto",
                        }}
                      >
                        Leg ReExecution Saftey Seconds
                      </label>
                      <input
                        type="text"
                        style={{
                          width: "190px",
                          padding: "5px 0px",
                          border: "1px solid #000",
                          borderRadius: "3px",
                        }}
                      />
                    </div>
                    <div style={{ margin: "10px 0px" }}>
                      <label
                        style={{
                          padding: "5px 0px",
                          fontSize: "15px",
                          lineHeight: "21px",
                          color: "#000",
                          fontWeight: "500",
                          fontFamily: "Roboto",
                        }}
                      >
                        ReExecute Count here will be applicable on portfolio
                        ReExecution only. For Leg ReEntry / ReExecute set the
                        count in portfolio Name fields
                      </label>
                    </div>
                  </div>

                  <div>
                    <div className="othersecone">
                      <fieldset
                        style={{
                          maxwidth: "100%",
                          padding: "5px",
                          borderRadius: "5px",
                          maxHeight: "100%",
                          boxSizing: "border-box",
                          fontFamily: "Arial, sans-serif",
                        }}
                      >
                        <legend
                          style={{
                            padding: "4px ",
                            fontSize: "18px",
                            lineHeight: "21px",
                            color: "#000",
                            fontWeight: "600",
                            fontFamily: "Roboto",
                            display: "block",
                          }}
                        >
                          Execute ReExecute Delays for Legs
                        </legend>

                        <div className="other-inputsone">
                          <label style={{ color: "#4661bd" }}>
                            Slicing Type
                          </label>
                        </div>
                        <div className="other-inputs">
                          <select
                            type="text"
                            style={{
                              width: "150px",
                              padding: "5px 0px",
                              border: "1px solid #000",
                              borderRadius: "3px",
                            }}
                          >
                            <option value="">Not Reqd</option>
                          </select>
                        </div>
                      </fieldset>
                    </div>

                    <label>
                      {" "}
                      <input type="checkbox" /> If One Side Activated , then
                      Cancle other Side{" "}
                    </label>
                    <label>(Applicable With W&T and Range Breakout Only)</label>
                    <label>
                      <input type="checkbox" /> No ReEntry
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="gap1">
          <div className="line">
            <table>
              <td className="OPN">
                <th>Option Portfolio Name</th>
                <input
                  defaultValue={
                    editPortfolio
                      ? portfolio.portfolio_name.toUpperCase()
                      : null
                  }
                  readOnly={editPortfolio}
                  type="text"
                  onChange={(e) =>
                    setPortfolioName(e.target.value.toUpperCase())
                  }
                  value={portfolio_name}
                />
              </td>
              <td className="Remarks">
                <th></th>
                <input type="text" placeholder="Remarks" />
              </td>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AddPortfolio;


