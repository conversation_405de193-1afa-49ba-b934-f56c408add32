/* Modern Strategy Selector Styles */
.strategy-selector-container {
  display: flex;
  align-items: center;
  gap: 0;
  width: 180px;
  font-family: "Roboto", sans-serif;
}

.strategy-label {
  font-size: 14px;
  font-weight: 600;
  color: #4661bd;
  margin-bottom: 4px;
}

.strategy-selector {
  position: relative;
  width: 150px;
  margin-right: 20px;
}

.dropdown-button {
  width: 110%;
  min-height: 36px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-right: 10px;
}

.dropdown-button:hover {
  border-color: #4661bd;
  box-shadow: 0 4px 12px rgba(70, 97, 189, 0.15);
  transform: translateY(-1px);
}

.dropdown-button:focus {
  outline: none;
  border-color: #4661bd;
  box-shadow: 0 0 0 3px rgba(70, 97, 189, 0.1);
}

.dropdown-button:disabled {
  background: #f1f5f9;
  border-color: #e2e8f0;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.dropdown-arrow {
  font-size: 12px;
  color: #6b7280;
  transition: transform 0.2s ease;
  margin-left: 8px;
}

/* Modern Dropdown Menu Styles */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #ffffff;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  margin-top: 4px;
  max-height: 300px;
  overflow-y: auto;
  backdrop-filter: blur(10px);
  animation: dropdownSlideIn 0.2s ease-out;
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-menu::-webkit-scrollbar {
  width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  background: #4661bd;
  border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #3b4f9a;
}

.category {
  border-bottom: 1px solid #f1f5f9;
}

.category:last-child {
  border-bottom: none;
}

.category-header {
  font-size: 14px;
  font-weight: 600;
  color: #4661bd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transition: all 0.2s ease;
}

.category-header:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  color: #3b4f9a;
}

.category-icon {
  font-size: 10px;
  color: #6b7280;
  transition: transform 0.2s ease;
}

.sub-options {
  background-color: #ffffff;
}

.option {
  font-size: 13px;
  color: #374151;
  cursor: pointer;
  padding: 8px 12px 8px 24px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  font-weight: 500;
}

.option:hover {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1d4ed8;
  transform: translateX(2px);
}

.option.selected {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1d4ed8;
  font-weight: 600;
}

.option::before {
  content: "├─ ";
  color: #9ca3af;
  margin-right: 6px;
  font-size: 11px;
}
.option.selected {
  background-color: #e6f7ff;
  font-weight: bold;
  border-left: 3px solid #d8e1ff;
}

.option:hover:not(.selected) {
  background-color: #f0f0f0;
}
